"""
Main entry point for the test case management application.
This module imports and runs the Streamlit application.
"""

import streamlit as st

# Initialize the database
from Test_case_db_helper import init_db, DATABASE_PATH, verify_and_update_schema, update_existing_test_cases, detect_schema_version

# Force reload of modules to ensure latest changes are used
import importlib
import sys
import gui
import gui.test_analysis
import gui.test_generator
import gui.app
import gui.sidebar
import gui.utils
import gui.visualization
import gui.reporting

# Remove all gui modules from cache to force a complete reload
for module_name in list(sys.modules.keys()):
    if module_name.startswith('gui'):
        del sys.modules[module_name]

# Reimport the modules
import gui
import gui.test_analysis
import gui.test_generator
import gui.app
import gui.sidebar
import gui.utils
import gui.visualization
import gui.reporting

# Initialize the database
print("Initializing database...")
init_db(DATABASE_PATH)
print("Database initialization complete")

# Update the database schema
print("Verifying and updating database schema...")
schema_version = detect_schema_version(DATABASE_PATH)
print(f"Detected schema version: {schema_version}")
verify_and_update_schema(DATABASE_PATH)
print("Schema verification and update complete")

# Update existing test cases with default values
print("Updating existing test cases with default values...")
update_existing_test_cases(DATABASE_PATH)
print("Test case update complete")

if __name__ == "__main__":
    # Import and run the Streamlit application
    from gui.app import run_app
    run_app()
