#!/usr/bin/env python3
"""
Repository Cleanup Script for GRETAH-CaseForge
Safely removes temporary files, cache files, and duplicates.
"""

import os
import shutil
import glob
from pathlib import Path
from datetime import datetime, timedelta
import argparse

def get_file_size(file_path):
    """Get file size in bytes."""
    try:
        return os.path.getsize(file_path)
    except:
        return 0

def format_size(size_bytes):
    """Format size in human readable format."""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

def clean_pycache(root_dir, dry_run=True):
    """Remove all __pycache__ directories."""
    removed_count = 0
    total_size = 0
    
    for root, dirs, files in os.walk(root_dir):
        if '__pycache__' in dirs:
            pycache_path = os.path.join(root, '__pycache__')
            
            # Calculate size
            for file in os.listdir(pycache_path):
                file_path = os.path.join(pycache_path, file)
                if os.path.isfile(file_path):
                    total_size += get_file_size(file_path)
            
            if dry_run:
                print(f"Would remove: {pycache_path}")
            else:
                shutil.rmtree(pycache_path)
                print(f"Removed: {pycache_path}")
            
            removed_count += 1
    
    return removed_count, total_size

def clean_temp_uploads(root_dir, dry_run=True):
    """Remove temporary upload files."""
    temp_dirs = [
        "GretahAI_ScriptWeaver/temp_uploads",
        "GretahAI_CaseForge/gui/temp_excel",
        "temp_uploads"
    ]
    
    removed_count = 0
    total_size = 0
    
    for temp_dir in temp_dirs:
        full_path = os.path.join(root_dir, temp_dir)
        if os.path.exists(full_path):
            for file in os.listdir(full_path):
                file_path = os.path.join(full_path, file)
                if os.path.isfile(file_path):
                    total_size += get_file_size(file_path)
                    
                    if dry_run:
                        print(f"Would remove: {file_path}")
                    else:
                        os.remove(file_path)
                        print(f"Removed: {file_path}")
                    
                    removed_count += 1
    
    return removed_count, total_size

def clean_log_files(root_dir, dry_run=True):
    """Remove debug and log files."""
    log_patterns = [
        "GretahAI_CaseForge/admin_config_debug.log",
        "GretahAI_CaseForge/gui/admin_config_debug.log",
        "GretahAI_CaseForge/test_execution.log",
        "test_stage8_optimization.log"
    ]
    
    removed_count = 0
    total_size = 0
    
    for pattern in log_patterns:
        full_path = os.path.join(root_dir, pattern)
        if os.path.exists(full_path):
            total_size += get_file_size(full_path)
            
            if dry_run:
                print(f"Would remove: {full_path}")
            else:
                os.remove(full_path)
                print(f"Removed: {full_path}")
            
            removed_count += 1
    
    return removed_count, total_size

def clean_duplicate_files(root_dir, dry_run=True):
    """Remove duplicate files."""
    duplicates = [
        "GretahAI_CaseForge/clear_database.sql"  # Duplicate of gui/clear_database.sql
    ]
    
    removed_count = 0
    total_size = 0
    
    for duplicate in duplicates:
        full_path = os.path.join(root_dir, duplicate)
        if os.path.exists(full_path):
            total_size += get_file_size(full_path)
            
            if dry_run:
                print(f"Would remove duplicate: {full_path}")
            else:
                os.remove(full_path)
                print(f"Removed duplicate: {full_path}")
            
            removed_count += 1
    
    return removed_count, total_size

def clean_old_backups(root_dir, keep_count=10, dry_run=True):
    """Remove old database backups, keeping the most recent ones."""
    backup_dir = os.path.join(root_dir, "GretahAI_CaseForge/backups")
    
    if not os.path.exists(backup_dir):
        return 0, 0
    
    # Get all backup files sorted by modification time
    backup_files = []
    for file in os.listdir(backup_dir):
        if file.startswith("database_backup_") and file.endswith(".db"):
            file_path = os.path.join(backup_dir, file)
            backup_files.append((file_path, os.path.getmtime(file_path)))
    
    # Sort by modification time (newest first)
    backup_files.sort(key=lambda x: x[1], reverse=True)
    
    # Remove old backups beyond keep_count
    removed_count = 0
    total_size = 0
    
    for file_path, _ in backup_files[keep_count:]:
        total_size += get_file_size(file_path)
        
        if dry_run:
            print(f"Would remove old backup: {file_path}")
        else:
            os.remove(file_path)
            print(f"Removed old backup: {file_path}")
        
        removed_count += 1
    
    return removed_count, total_size

def main():
    parser = argparse.ArgumentParser(description="Clean up GRETAH-CaseForge repository")
    parser.add_argument("--dry-run", action="store_true", default=True,
                       help="Show what would be deleted without actually deleting")
    parser.add_argument("--execute", action="store_true",
                       help="Actually perform the cleanup (overrides --dry-run)")
    parser.add_argument("--keep-backups", type=int, default=10,
                       help="Number of recent database backups to keep")
    
    args = parser.parse_args()
    
    # If --execute is specified, turn off dry_run
    dry_run = not args.execute
    
    root_dir = os.path.dirname(os.path.abspath(__file__))
    
    print("=" * 60)
    print("GRETAH-CaseForge Repository Cleanup")
    print("=" * 60)
    print(f"Mode: {'DRY RUN' if dry_run else 'EXECUTE'}")
    print(f"Root directory: {root_dir}")
    print()
    
    total_files = 0
    total_size = 0
    
    # Clean Python cache files
    print("🗑️  Cleaning Python cache files...")
    count, size = clean_pycache(root_dir, dry_run)
    total_files += count
    total_size += size
    print(f"   {count} directories, {format_size(size)}")
    print()
    
    # Clean temporary uploads
    print("🗑️  Cleaning temporary upload files...")
    count, size = clean_temp_uploads(root_dir, dry_run)
    total_files += count
    total_size += size
    print(f"   {count} files, {format_size(size)}")
    print()
    
    # Clean log files
    print("🗑️  Cleaning log and debug files...")
    count, size = clean_log_files(root_dir, dry_run)
    total_files += count
    total_size += size
    print(f"   {count} files, {format_size(size)}")
    print()
    
    # Clean duplicate files
    print("🗑️  Cleaning duplicate files...")
    count, size = clean_duplicate_files(root_dir, dry_run)
    total_files += count
    total_size += size
    print(f"   {count} files, {format_size(size)}")
    print()
    
    # Clean old backups
    print(f"🗑️  Cleaning old database backups (keeping {args.keep_backups})...")
    count, size = clean_old_backups(root_dir, args.keep_backups, dry_run)
    total_files += count
    total_size += size
    print(f"   {count} files, {format_size(size)}")
    print()
    
    print("=" * 60)
    print(f"Total: {total_files} items, {format_size(total_size)}")
    
    if dry_run:
        print("\n⚠️  This was a DRY RUN. No files were actually deleted.")
        print("   Run with --execute to perform the actual cleanup.")
    else:
        print("\n✅ Cleanup completed successfully!")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
