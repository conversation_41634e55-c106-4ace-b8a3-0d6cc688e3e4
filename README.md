# GRETAH-AI: AI-Powered Test Automation Suite

<table align="center" border="0" cellpadding="0" cellspacing="0" style="border:none;">
  <tr>
    <td align="center" style="border:none;">
      <img src="https://cogniron.com/wp-content/uploads/2024/10/image-69.png"
           alt="Cogniron Logo"
           width="300" />
    </td>
    <td align="center" style="border:none;">
      <img src="./Gretah_logo.png"
           alt="GretahAI Logo"
           width="300" />
    </td>
  </tr>
</table>



GRETAH-AI is a comprehensive suite of AI-powered applications designed to streamline the entire test automation lifecycle, from test case generation to execution analysis and reporting. This repository contains the source code for the suite.
---

## Contents

1. [Applications](#applications)  
2. [Quick Start](#quick-start)  
3. [Prerequisites](#prerequisites)  
4. [Configuration](#configuration)  
5. [Repository Structure](#repository-structure)  
6. [Key Features](#key-features)
7. [Support](#support)

---

## Applications

| Application | Summary |
|-------------|---------|
| **[CaseForge](./GretahAI_CaseForge/README.md)** | Test-case generation and management |
| **[ScriptWeaver](./GretahAI_ScriptWeaver/README.md)** | PyTest script generation | 
| **[TestInsight](./GretahAI_TestInsight/README.md)** | Execution analysis and reporting |

---

## Quick Start

Each application has its own README; refer there for full details.

```bash
# Clone the repository
git clone <repo-url>
cd GRETAH-AI

# Create a virtual environment
python -m venv venv
# Windows
.\venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# Install shared dependencies
pip install -r requirements.txt
```

### Prerequisites

1. Python 3.9 or later
2. Streamlit
3. Pandas
4. SQLite (bundled with Python)
5. Ollama (local LLM runtime)
6. Google AI Studio API key
7. JIRA account and API token

## Configuration

**Configure API Keys and Settings:**
   Create or update the following JSON files in the repository root:
   - `config.json`: Contains API keys and connection details.
     ```json
     {
       "google_api_key": "YOUR_GOOGLE_AI_STUDIO_API_KEY",
       "jira_server": "YOUR_JIRA_SERVER_URL",
       "jira_user": "YOUR_JIRA_USERNAME",
       "jira_api_token": "YOUR_JIRA_API_TOKEN"
     }
     ```
   - `admin_config.json`: Contains admin panel settings and user credentials.
     ```json
     {
       "allow_delete_test_cases": false,
       "allow_clear_database": false,
       "users": [
         {
           "username": "admin",
           "password_hash": "hashed_password_here",
           "role": "admin",
           "created_at": "YYYY-MM-DDTHH:MM:SS"
         }
         // Add more users as needed
       ]
     }
     ```
     *(Note: Passwords should be stored as secure hashes, not plain text. The application's admin panel handles user creation and password hashing.)*

5. **Database Setup (CaseForge):**
   The CaseForge application uses an SQLite database (`test_cases_v2.db`). This file should be present in the project structure. If it's missing, the application might attempt to create it or fail. Ensure the database file is correctly located as expected by the `Test_case_db_helper.py` module.

## Repository Structure

```
GRETAH-AI/
├── GretahAI_CaseForge/       # Test case management application
│   ├── gui/                  # Streamlit UI components (app.py, sidebar.py, style.css, etc.)
│   ├── db/                   # Database utilities (Test_case_db_helper.py)
│   ├── utils/                # Utility functions
│   ├── helpers.py            # Helper functions (Excel, Ollama, etc.)
│   └── README.md             # CaseForge documentation
│
├── GretahAI_ScriptWeaver/    # Test script generation application
│   ├── core/                 # Core functionality
│   ├── utils/                # Utility functions
│   └── README.md             # ScriptWeaver documentation
│
├── GretahAI_TestInsight/     # Test execution & analysis application
│   ├── logs/                 # Test execution logs
│   ├── screenshots/          # Test screenshots
│   ├── raw_outputs/          # AI summaries and reports
│   └── README.md             # TestInsight documentation
│
├── Gretah_logo.png           # Local GretahAI logo file
├── config.json               # Configuration file with API keys
├── admin_config.json         # Admin panel configuration and users
├── requirements.txt          # Python dependencies
├── test_cases_v2.db          # SQLite Database for CaseForge
└── README.md                 # This file
```

## Key Features

- **AI-Powered Test Case Generation:** Leverage AI models (local or cloud) to automatically generate test scenarios and steps from JIRA issue descriptions.
- **Structured Test Case Management:** Store, view, and manage generated and manually created test cases in a structured database.
- **Excel Import/Export:** Easily import test cases from Excel or export generated cases for review and editing.
- **JIRA Integration:** Fetch issue details directly from JIRA and potentially link test cases back (depending on specific integration features).
- **Test Execution Tracking (Basic):** Record and view results of test case executions within the application.
- **Admin Controls:** Manage application users, configure administrative settings, and perform database maintenance operations.
- **Test Script Generation (ScriptWeaver):** Convert structured test cases into executable automation scripts.
- **Execution Analysis & Reporting (TestInsight):** Monitor test runs, analyze logs, and visualize results with AI assistance.


## Support

For questions, feature requests, or contributions, open an issue <NAME_EMAIL> 
