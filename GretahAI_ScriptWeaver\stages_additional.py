"""
Additional stage functions for the GretahAI ScriptWeaver application.
Each stage function handles a specific part of the application flow.
"""

import os
import logging
import streamlit as st
from pathlib import Path
from datetime import datetime, timedelta
import time
import re
from core.ai import merge_scripts_with_ai

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.stages_additional")

def create_combined_script(state):
    """
    Create a combined script file containing all steps for the current test case.

    This should be called after all steps have been processed (when state.all_steps_done is True).
    The combined script will merge all individual step scripts in the correct order.

    Args:
        state (StateManager): The application state manager instance

    Returns:
        str: Path to the combined script file, or None if creation failed
    """
    try:
        # Check if we have a test case and previous scripts
        if not hasattr(state, 'selected_test_case') or not state.selected_test_case:
            logger.warning("No selected test case found, cannot create combined script")
            return None

        if not hasattr(state, 'previous_scripts') or not state.previous_scripts:
            logger.warning("No previous scripts found, cannot create combined script")
            return None

        # Get the test case ID
        test_case_id = state.selected_test_case.get('Test Case ID', 'unknown')

        # Get all step numbers in order
        step_numbers = sorted(state.previous_scripts.keys(), key=int)

        if not step_numbers:
            logger.warning("No step scripts found, cannot create combined script")
            return None

        logger.info(f"Creating combined script for test case {test_case_id} with steps: {', '.join(step_numbers)}")

        # Start with the first step's script
        combined_script = state.previous_scripts[step_numbers[0]]

        # Add a header comment to the combined script
        header = f"""# Combined Test Script for Test Case: {test_case_id}
# Generated on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
# Includes steps: {', '.join(step_numbers)}
"""
        combined_script = header + combined_script

        # Merge each subsequent step's script
        for i in range(1, len(step_numbers)):
            current_step = step_numbers[i]
            current_script = state.previous_scripts[current_step]

            # Use AI-powered merge
            logger.info(f"Merging step {current_step} into combined script using AI")
            combined_script = merge_scripts_with_ai(combined_script, current_script, api_key=state.google_api_key)

        # Create a file path for the combined script
        script_dir = "generated_tests"
        os.makedirs(script_dir, exist_ok=True)
        combined_script_file = os.path.join(
            script_dir,
            f"test_{test_case_id}_combined_{int(time.time())}.py"
        )

        # Save the combined script to a file
        with open(combined_script_file, "w") as f:
            f.write(combined_script)

        logger.info(f"Successfully created combined script file: {combined_script_file}")
        return combined_script_file

    except Exception as e:
        logger.error(f"Error creating combined script: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None

# Import helper functions from other modules
from core.element_detection import detect_elements, detect_elements_advanced, filter_qa_relevant_elements, select_element_interactively
from core.element_matching import match_elements_with_ai
from core.helpers_diff import generate_html_diff, analyze_script_origins, generate_annotated_script, generate_colored_html_script
from helpers_pure import analyze_step_for_test_data

def advance_to_next_step():
    """
    Advance to the next test case step.

    This function:
    1. Increments the current step index
    2. Selects the next test case step from the step table
    3. Updates state manager variables
    4. Returns True if there are more steps, False if all steps are processed

    Returns:
        bool: True if successfully advanced to the next test case step, False if all steps are processed
    """
    logger.info("=== advance_to_next_step() called ===")

    # Get state manager
    from state_manager import StateManager
    state = StateManager.get(st)

    # Check if we have a step table
    if not state.step_table_json or not isinstance(state.step_table_json, list):
        logger.warning("No valid step_table_json found")
        return False

    # Get the total number of steps if not already set
    if state.total_steps == 0:
        state.total_steps = len(state.step_table_json)
        logger.info(f"State change: total_steps = {state.total_steps}")

    # Log detailed state information before advancing
    logger.info(f"State before advancing: current_step_index = {state.current_step_index}")
    logger.info(f"State before advancing: total_steps = {state.total_steps}")
    logger.info(f"State before advancing: step_ready_for_script = {state.step_ready_for_script}")
    logger.info(f"State before advancing: all_steps_done = {state.all_steps_done}")

    # Also log to Streamlit session state for debugging
    st.session_state['debug_before_advance'] = {
        'current_step_index': state.current_step_index,
        'total_steps': state.total_steps,
        'step_ready_for_script': state.step_ready_for_script,
        'all_steps_done': state.all_steps_done,
        'timestamp': datetime.now().strftime("%H:%M:%S.%f")
    }

    # Save the current step information before advancing
    current_step_info = None
    if hasattr(state, 'selected_step') and state.selected_step:
        current_step_info = {
            'step_no': state.selected_step.get('Step No', 'Unknown'),
            'test_steps': state.selected_step.get('Test Steps', ''),
            'expected_result': state.selected_step.get('Expected Result', '')
        }
        logger.info(f"Current step before advancing: {current_step_info['step_no']}")

    # Use the state manager's update method to increment the step index
    new_step_index = state.current_step_index + 1
    state.update_step_progress(current_step_index=new_step_index)

    # Check if we've processed all steps
    if new_step_index >= state.total_steps:
        logger.info("All steps processed, setting all_steps_done to True")
        state.update_step_progress(all_steps_done=True)

        # Add a message to session state for display after rerun
        st.session_state['stage_progression_message'] = "✅ All test case steps have been processed!"

        # Force state update in session state
        st.session_state['state'] = state

        # Immediately rerun to refresh the UI with the new state
        st.rerun()
        # Return statement will never be reached due to rerun, but included for clarity
        return False

    # Get the next step from the step table
    next_step = state.step_table_json[state.current_step_index]
    logger.info(f"Next step: step_no = {next_step.get('step_no')}, action = {next_step.get('action')}")

    # Find the corresponding original step
    if state.selected_test_case and 'Steps' in state.selected_test_case:
        original_steps = state.selected_test_case.get('Steps', [])
        step_no = str(next_step.get('step_no', ''))
        logger.info(f"Looking for original step with Step No: {step_no}")

        try:
            selected_original_step = next(
                (step for step in original_steps if str(step.get('Step No')) == step_no),
                None
            )

            if selected_original_step:
                logger.info(f"Found original step: Step No = {selected_original_step.get('Step No')}")
            else:
                logger.warning(f"Original step not found for step_no: {step_no}")
                logger.warning(f"Available steps: {[str(step.get('Step No')) for step in original_steps]}")
        except Exception as e:
            logger.error(f"Error finding original step: {e}")
            selected_original_step = None

        if selected_original_step and next_step:
            # Save context from the current step before updating state
            if current_step_info:
                # Mark the current step as completed
                current_step_no = current_step_info['step_no']

                # Initialize completed_steps if not present
                if not hasattr(state, 'completed_steps') or not isinstance(state.completed_steps, list):
                    state.completed_steps = []

                # Add current step to completed steps if not already there
                if current_step_no not in state.completed_steps:
                    state.completed_steps.append(current_step_no)
                    logger.info(f"State change: added step {current_step_no} to completed_steps")

                # Store context from the current step that might be useful for the next step
                if not hasattr(state, 'step_context') or not isinstance(state.step_context, dict):
                    state.step_context = {}

                # Save relevant context from the current step
                state.step_context[current_step_no] = {
                    "elements": state.step_elements if hasattr(state, 'step_elements') else [],
                    "matches": state.step_matches if hasattr(state, 'step_matches') else {},
                    "test_data": state.test_data if hasattr(state, 'test_data') else {},
                    "script_path": state.generated_script_path if hasattr(state, 'generated_script_path') else None
                }
                # Log state change for context saving
                previous_context = state.step_context.get(current_step_no, None)
                if previous_context != state.step_context[current_step_no]:
                    logger.info(f"State change: saved context for step {current_step_no}")

            # Update state manager with the new step
            state.selected_step_table_entry = next_step
            state.selected_step = selected_original_step
            # Log state change for step selection
            previous_step_no = state.selected_step.get('Step No') if hasattr(state, 'selected_step') and state.selected_step else None
            new_step_no = selected_original_step.get('Step No')
            if previous_step_no != new_step_no:
                logger.info(f"State change: updated selected_step_table_entry and selected_step to Step No {new_step_no}")

            # Reset step-specific state variables
            state.step_elements = []
            state.step_matches = {}
            state.test_data = {}
            state.test_data_skipped = False
            state.llm_step_analysis = {}
            state.step_ready_for_script = False
            state.script_just_generated = False
            # Only log if there were actual changes to reset
            if (hasattr(state, 'step_elements') and state.step_elements) or \
               (hasattr(state, 'step_matches') and state.step_matches) or \
               (hasattr(state, 'test_data') and state.test_data) or \
               (hasattr(state, 'test_data_skipped') and state.test_data_skipped) or \
               (hasattr(state, 'llm_step_analysis') and state.llm_step_analysis) or \
               (hasattr(state, 'step_ready_for_script') and state.step_ready_for_script) or \
               (hasattr(state, 'script_just_generated') and state.script_just_generated):
                logger.info("State change: reset step-specific state variables")

            # Log detailed state information after advancing
            logger.info(f"State after advancing: current_step_index = {state.current_step_index}")
            logger.info(f"State after advancing: total_steps = {state.total_steps}")
            logger.info(f"State after advancing: step_ready_for_script = {state.step_ready_for_script}")
            logger.info(f"State after advancing: all_steps_done = {state.all_steps_done}")
            logger.info(f"State after advancing: selected_step.Step No = {state.selected_step.get('Step No')}")

            # Also log to Streamlit session state for debugging
            st.session_state['debug_after_advance'] = {
                'current_step_index': state.current_step_index,
                'total_steps': state.total_steps,
                'step_ready_for_script': state.step_ready_for_script,
                'all_steps_done': state.all_steps_done,
                'selected_step_no': state.selected_step.get('Step No'),
                'timestamp': datetime.now().strftime("%H:%M:%S.%f")
            }

            # Add a message to session state for display after rerun
            next_step_no = selected_original_step.get('Step No')
            next_step_action = selected_original_step.get('Test Steps', '')[:50]  # Truncate for display
            st.session_state['stage_progression_message'] = f"✅ Advanced to Test Case Step {next_step_no}: {next_step_action}..."

            # Force the state to be updated in the session state
            st.session_state['state'] = state

            logger.info(f"Successfully advanced to next step: {next_step_no}")
            # Immediately rerun to refresh the UI with the new state
            st.rerun()
            # Return statement will never be reached due to rerun, but included for clarity
            return True
        else:
            logger.warning(f"Failed to advance: selected_original_step={selected_original_step is not None}, next_step={next_step is not None}")
    else:
        logger.warning("No selected_test_case or no Steps in selected_test_case")

    logger.warning("Failed to advance to next step")
    return False

def _show_next_step_banner(state, next_step_no, next_step_action):
    """
    Display a banner indicating the next step is ready.

    Creates a visually distinct banner with information about the completed step
    and the next step that the user should proceed to.

    Args:
        state (StateManager): The application state manager instance
        next_step_no (str): The step number of the next step
        next_step_action (str): The action description of the next step
    """
    st.markdown(f"""
    <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: #f0f7ff; border-radius: 10px; border: 2px solid #4A6FE3;">
        <h3 style="color: #4A6FE3; margin-bottom: 10px;">Ready for next test case step</h3>
        <p style="font-size: 14px;">Test Case Step {state.selected_step.get('Step No')} completed. Click to proceed to Test Case Step {next_step_no}.</p>
    </div>
    """, unsafe_allow_html=True)

def _create_proceed_button(state, next_step_no, next_step_action, stage_key):
    """
    Create a button to proceed to the next step.

    Creates a centered button that, when clicked, advances the application
    to the next step in the test case workflow. The button includes information
    about the next step and handles the state transitions when clicked.

    Args:
        state (StateManager): The application state manager instance
        next_step_no (str): The step number of the next step
        next_step_action (str): The action description of the next step
        stage_key (str): A unique identifier for the button based on the current stage
    """
    # Use columns to center the button
    _, proceed_col, _ = st.columns([1, 2, 1])
    with proceed_col:
        # Define the button with a direct action handler - clarify this is a test case step, not application stage
        button_label = f"Proceed to Test Case Step {next_step_no}"
        if next_step_action:
            # Truncate action text if too long
            action_text = next_step_action[:40] + "..." if len(next_step_action) > 40 else next_step_action
            button_label = f"{button_label} - {action_text}"

        if st.button(button_label,
                    key=f"proceed_to_next_step_{stage_key}",
                    use_container_width=True):

            with st.spinner(f"Advancing to Test Case Step {next_step_no}..."):
                # Add a small delay to allow the user to see the spinner
                time.sleep(0.5)

                # Log the current state before advancing
                logger.info(f"Button clicked: Proceeding to Test Case Step {next_step_no}")
                logger.info(f"Before advancing: current_step_index = {state.current_step_index}, total_steps = {state.total_steps}")

                # Store the current step index for comparison
                old_step_index = state.current_step_index

                # Reset the flags
                if state.step_ready_for_script:
                    state.step_ready_for_script = False
                    logger.info("State change: step_ready_for_script = False")

                # Store the current timestamp for debugging
                proceed_timestamp = datetime.now().strftime("%H:%M:%S.%f")
                logger.info(f"Proceed button clicked at: {proceed_timestamp}")

                # Store button click info in session state for debugging
                st.session_state['proceed_button_info'] = {
                    'clicked_at': proceed_timestamp,
                    'next_step_no': next_step_no,
                    'stage_key': stage_key,
                    'current_step_index': state.current_step_index
                }

                # Log the state before advancing
                logger.info(f"Before advancing: current_step_index = {state.current_step_index}, total_steps = {state.total_steps}")

                # Advance to the next step
                # This will call st.rerun() internally if successful
                advance_to_next_step()

                # The code below will only execute if advance_to_next_step() fails
                # and doesn't trigger a rerun

                # Check if the step index changed anyway (unlikely to reach this code)
                if state.current_step_index > old_step_index:
                    # Success! The step index changed but rerun didn't happen for some reason
                    logger.info(f"Successfully advanced to step {state.current_step_index + 1} but rerun didn't happen")

                    # Force state update in session state before rerun
                    st.session_state['state'] = state

                    # Force a rerun to refresh the UI with the new step
                    st.rerun()
                else:
                    # All steps processed or something went wrong
                    logger.warning("advance_to_next_step() didn't trigger a rerun - handling fallback")

                    # Check if we've processed all steps
                    if state.all_steps_done:
                        st.success("✅ All test case steps have been processed!")
                        logger.info("All steps processed, all_steps_done = True")

                        # Force state update in session state
                        st.session_state['state'] = state
                        st.session_state['stage_progression_message'] = "✅ All test case steps have been processed!"
                        st.rerun()
                    else:
                        # Something went wrong
                        st.error("Failed to advance to the next step. Please check the logs.")
                        logger.error("Failed to advance to the next step")

def _display_workflow_summary(state):
    """
    Display a summary of the current workflow state.

    Shows the hierarchical progression from suite to test case to step level,
    highlighting the current position in the workflow.

    Args:
        state (StateManager): The application state manager instance
    """
    # Create a container for the workflow summary
    with st.container():
        st.markdown("### Workflow Progress")

        # Create three columns for the three levels
        col1, col2, col3 = st.columns(3)

        # Suite level (Stage 1)
        with col1:
            if hasattr(state, 'test_cases') and state.test_cases:
                test_case_count = len(state.test_cases)
                st.success(f"✅ Suite: {test_case_count} test cases loaded")
            else:
                st.warning("⚠️ Suite: No test cases loaded")

        # Test case level (Stage 3)
        with col2:
            if hasattr(state, 'selected_test_case') and state.selected_test_case:
                tc_id = state.selected_test_case.get('Test Case ID', 'Unknown')
                st.success(f"✅ Test Case: {tc_id} selected")
            else:
                st.warning("⚠️ Test Case: None selected")

        # Step level (Stage 4+)
        with col3:
            if hasattr(state, 'selected_step') and state.selected_step:
                step_no = state.selected_step.get('Step No', 'Unknown')
                current_step = state.current_step_index + 1
                total_steps = state.total_steps
                st.success(f"✅ Step: {step_no} ({current_step}/{total_steps})")
            else:
                st.warning("⚠️ Step: None selected")

        # Add a progress bar for overall workflow
        workflow_stage = 1  # Default to Stage 1

        if hasattr(state, 'test_cases') and state.test_cases:
            workflow_stage = 2  # Completed Stage 1

            if hasattr(state, 'selected_test_case') and state.selected_test_case:
                workflow_stage = 3  # Completed Stage 2

                if hasattr(state, 'step_table_json') and state.step_table_json:
                    workflow_stage = 4  # Completed Stage 3

                    if hasattr(state, 'selected_step') and state.selected_step:
                        workflow_stage = 5  # Working on Stage 4+

        # Calculate progress percentage (7 stages total)
        progress_percentage = workflow_stage / 7
        st.progress(progress_percentage)

def stage4_ui_detection_and_matching(state):
    """Application Stage 4: UI Element Detection and Test Case Step Selection."""
    st.markdown("<h2 class='stage-header'>Application Stage 4: UI Element Detection and Test Case Step Selection</h2>", unsafe_allow_html=True)

    # Check if we have a stage progression message to display
    if 'stage_progression_message' in st.session_state:
        st.success(st.session_state['stage_progression_message'])
        # Remove the message so it doesn't show up again
        del st.session_state['stage_progression_message']

    # Check if we're coming from Stage 7 (automatic advancement)
    if 'coming_from_stage7' in st.session_state:
        # Get information about the advancement
        if 'force_refresh_after_advance' in st.session_state:
            from_step = st.session_state['force_refresh_after_advance'].get('from_step')
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')

            # Display a prominent banner
            st.markdown(f"""
            <div style="text-align: center; margin: 20px 0; padding: 15px; background-color: #e8f5e9; border-radius: 10px; border: 2px solid #4CAF50;">
                <h3 style="color: #4CAF50; margin-bottom: 10px;">✅ Automatic Advancement</h3>
                <p style="font-size: 16px;">Successfully completed Test Case Step {from_step} and advanced to Step {target_step}.</p>
                <p style="font-size: 14px; margin-top: 10px;">Continue with UI Element Detection for this step.</p>
            </div>
            """, unsafe_allow_html=True)

        # Clear the flag so it doesn't show up again
        del st.session_state['coming_from_stage7']

    # Check if we need to force a refresh after automatic advancement
    if 'force_refresh_after_advance' in st.session_state:
        # Get the timestamp to see if this is a recent advancement
        advance_time = st.session_state['force_refresh_after_advance'].get('timestamp')
        from datetime import datetime, timedelta
        now = datetime.now()

        # If the advancement was within the last 5 seconds, force a refresh
        if advance_time and (now - datetime.strptime(advance_time, "%H:%M:%S.%f")) < timedelta(seconds=5):
            # Clear the flag so we don't keep refreshing
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')
            logger.info(f"Forcing refresh to ensure UI shows step {target_step}")
            del st.session_state['force_refresh_after_advance']

            # Add a visible indicator that we're refreshing
            st.info(f"🔄 Refreshing to show step {target_step}...")

            # Force a rerun to refresh the UI
            time.sleep(0.5)
            st.rerun()

    # Check if prerequisites are met
    if not hasattr(state, 'step_table_json') or not state.step_table_json:
        st.warning("⚠️ Please complete Application Stage 3 first to convert the test case to a step table")
        return

    # Stage 4a: Select Test Step from Step Table
    st.subheader("4a. Test Case Step Selection and Progression")

    # Add information about manual step progression
    st.info("Test case steps are processed sequentially. After completing each test case step, use the 'Proceed to Next Test Case Step' button to advance.")

    # Add a reset button to start over with step 1
    reset_cols = st.columns([3, 1])
    with reset_cols[0]:
        if st.button("Reset to Test Case Step 1", key="reset_step_btn", help="Reset to the first step of the test case"):
            # Check if we have progress to confirm reset
            has_progress = (hasattr(state, 'current_step_index') and state.current_step_index > 0) or state.all_steps_done

            if has_progress:
                st.warning("⚠️ Resetting to Step 1 will clear all progress on the current test case steps.")
                confirm_reset = st.button("Confirm Reset", key="confirm_reset_step")

                if not confirm_reset:
                    st.info("Reset cancelled. Your progress is preserved.")
                    return

            # Use the state manager's update method to reset step progress
            state.update_step_progress(
                current_step_index=0,
                all_steps_done=False,
                step_ready_for_script=False,
                script_just_generated=False
            )

            # Reset step-specific state
            state.reset_step_state(confirm=True, reason="User requested reset to Step 1")

            st.success("✅ Reset to Test Case Step 1 complete.")
            st.rerun()

    # Show progress information
    # Get total steps if not already set
    if state.total_steps == 0 and state.step_table_json:
        state.total_steps = len(state.step_table_json)

    # Show progress bar
    if state.total_steps > 0:
        current_step = state.current_step_index + 1  # Add 1 for display (1-based indexing)
        progress_percentage = current_step / state.total_steps

        # Create columns for progress information
        prog_col1, prog_col2 = st.columns([3, 1])
        with prog_col1:
            st.progress(progress_percentage)
        with prog_col2:
            st.info(f"Step {current_step} of {state.total_steps}")

        # Add test case context
        if hasattr(state, 'selected_test_case') and state.selected_test_case:
            tc_id = state.selected_test_case.get('Test Case ID', 'Unknown')
            st.info(f"Test Case: {tc_id}")

    # Show completion message if all steps are processed
    if state.all_steps_done:
        st.success("✅ All test case steps have been processed!")
        st.info("You can reset to Test Case Step 1 using the button above if you want to start over.")
        return

    # Show "Proceed to next step" button if the current step is ready for script and there are more steps
    if state.step_ready_for_script:
        # Get the next step information
        next_step_index = state.current_step_index + 1
        if next_step_index < state.total_steps:
            next_step = state.step_table_json[next_step_index]
            next_step_no = next_step.get('step_no', 'N/A')
            next_step_action = next_step.get('action', 'N/A')

            # Add section for the next step button
            st.markdown("---")
            _show_next_step_banner(state, next_step_no, next_step_action)
            _create_proceed_button(state, next_step_no, next_step_action, "stage4")
            return

    # Get steps from the step table JSON
    step_table_json = state.step_table_json

    if not step_table_json or not isinstance(step_table_json, list):
        st.error("Step table JSON is not available or not in the correct format")
        return

    # Create step selection dropdown from the step table
    step_options = []
    for step in step_table_json:
        if isinstance(step, dict):
            step_no = step.get('step_no', 'N/A')
            action = step.get('action', 'N/A')
            step_options.append(f"Step {step_no} - {action}")

    if not step_options:
        st.warning("No steps found in the step table")
        return

    # Initialize to the first step if needed
    if state.current_step_index < 0 or state.current_step_index >= len(step_options):
        state.current_step_index = 0
        logger.info(f"Initializing current_step_index to 0 (was out of bounds)")

    # Log the current step index for debugging
    logger.info(f"Current step index: {state.current_step_index}, Total steps: {len(step_options)}")

    # Add debug information to help diagnose step selection issues
    if 'auto_advance_debug' in st.session_state:
        debug_info = st.session_state['auto_advance_debug']
        st.info(f"Debug: Last auto-advance at {debug_info['timestamp']} from step {debug_info['from_step']} to step {debug_info['to_step']}")

    # Get the current step option
    try:
        # Check if we're coming from Stage 7 with a specific step
        if 'coming_from_stage7' in st.session_state and 'force_refresh_after_advance' in st.session_state:
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')
            logger.info(f"Coming from Stage 7, looking for step {target_step}")

            # Find the step option that matches the target step
            matching_options = [opt for opt in step_options if f"Step {target_step}" in opt]
            if matching_options:
                # Find the index of the matching option
                target_index = step_options.index(matching_options[0])

                # If the current index doesn't match the target, update it
                if state.current_step_index != target_index:
                    logger.info(f"Updating current_step_index from {state.current_step_index} to {target_index} to match target step {target_step}")
                    state.current_step_index = target_index

        # Get the current step option based on the (possibly updated) index
        current_step_option = step_options[state.current_step_index]
        logger.info(f"Selected step option: {current_step_option}")
    except IndexError:
        # Handle index error gracefully
        logger.error(f"Index error: current_step_index={state.current_step_index}, len(step_options)={len(step_options)}")
        st.error(f"Error: Step index {state.current_step_index} is out of range (0-{len(step_options)-1})")
        # Reset to a valid index
        state.current_step_index = 0
        current_step_option = step_options[0]
        logger.info(f"Reset to step option: {current_step_option}")

    # Create columns for step selection and navigation
    step_col1, step_col2 = st.columns([3, 1])

    with step_col1:
        # Allow direct selection of steps with a selectbox
        # Default to the current step but allow changing
        selected_index = step_options.index(current_step_option)
        selected_step_option = st.selectbox(
            "Select Test Case Step",
            step_options,
            index=selected_index,
            help="Select a specific test case step to process"
        )

        # If user selected a different step, update the current step index
        if selected_step_option != current_step_option:
            new_index = step_options.index(selected_step_option)

            # Show a warning about changing steps
            st.warning(f"⚠️ Switching to step {new_index + 1}. Any unsaved progress on the current step will be lost.")

            # Add a confirmation button
            if st.button("Confirm Step Change", key="confirm_step_change"):
                # Check if the current step has any progress
                has_step_progress = (
                    (hasattr(state, 'step_elements') and state.step_elements) or
                    (hasattr(state, 'step_matches') and state.step_matches) or
                    (hasattr(state, 'test_data') and state.test_data) or
                    (hasattr(state, 'test_data_skipped') and state.test_data_skipped) or
                    (hasattr(state, 'step_ready_for_script') and state.step_ready_for_script) or
                    (hasattr(state, 'script_just_generated') and state.script_just_generated)
                )

                # Log the step change with progress information
                logger.info(f"Changing step from {state.current_step_index} to {new_index} (has_progress={has_step_progress})")

                # Use the state manager's update method to update step index
                state.update_step_progress(current_step_index=new_index)

                # Reset step-specific state
                state.reset_step_state(confirm=True, reason=f"User changed from step {state.current_step_index + 1} to step {new_index + 1}")

                st.success(f"✅ Changed to Step {new_index + 1}")
                st.rerun()

            # If not confirmed, revert to the current step
            selected_step_option = current_step_option

    with step_col2:
        # Add step completion tracking
        if hasattr(state, 'completed_steps') and isinstance(state.completed_steps, list):
            completed_count = len(state.completed_steps)
            st.metric("Steps Completed", f"{completed_count} of {state.total_steps}")
        else:
            # Initialize completed_steps if not present
            state.completed_steps = []
            st.metric("Steps Completed", f"0 of {state.total_steps}")

    # Set the selected step option for the rest of the code to use
    # This will be the current step unless the user confirmed a change

    if selected_step_option != "Select a step...":
        step_no = selected_step_option.split(" - ")[0].replace("Step ", "")
        logger.info(f"Processing step_no: {step_no} from selected_step_option: {selected_step_option}")

        # Find the selected step in the step table
        selected_step_table_entry = next(
            (step for step in step_table_json if str(step.get('step_no')) == step_no),
            None
        )

        if selected_step_table_entry:
            logger.info(f"Found step table entry for step {step_no}: {selected_step_table_entry.get('action')}")
        else:
            logger.error(f"Could not find step table entry for step {step_no}")
            # Log all available steps for debugging
            for step in step_table_json:
                logger.info(f"Available step: {step.get('step_no')} - {step.get('action')}")

        # Also find the corresponding original step
        original_steps = state.selected_test_case.get('Steps', [])
        selected_original_step = next(
            (step for step in original_steps if str(step.get('Step No')) == step_no),
            None
        )

        if selected_original_step:
            logger.info(f"Found original step for step {step_no}: {selected_original_step.get('Test Steps')}")
        else:
            logger.error(f"Could not find original step for step {step_no}")
            # Log all available original steps for debugging
            for step in original_steps:
                logger.info(f"Available original step: {step.get('Step No')} - {step.get('Test Steps')}")

        if selected_step_table_entry and selected_original_step:
            # Store both versions in state manager
            state.selected_step_table_entry = selected_step_table_entry
            state.selected_step = selected_original_step

            # Display step details in a more organized way
            st.markdown("### Selected Step Details")

            # Show context from previous steps if available
            _display_previous_step_context(state, selected_original_step)

            # Create tabs for different views of the step
            step_tab1, step_tab2 = st.tabs(["Automation-Ready Format", "Original Format"])

            with step_tab1:
                # Display the automation-ready step details
                col1, col2 = st.columns(2)
                with col1:
                    st.markdown(f"**Step Number:** {selected_step_table_entry.get('step_no')}")
                    st.markdown(f"**Action:** {selected_step_table_entry.get('action')}")
                    st.markdown(f"**Locator Strategy:** {selected_step_table_entry.get('locator_strategy')}")
                    st.markdown(f"**Locator:** {selected_step_table_entry.get('locator')}")
                with col2:
                    st.markdown(f"**Test Data Parameter:** {selected_step_table_entry.get('test_data_param')}")
                    st.markdown(f"**Expected Result:** {selected_step_table_entry.get('expected_result')}")
                    st.markdown(f"**Assertion Type:** {selected_step_table_entry.get('assertion_type')}")
                    st.markdown(f"**Timeout:** {selected_step_table_entry.get('timeout')} seconds")

            with step_tab2:
                # Display the original step details
                st.markdown(f"**Step Number:** {selected_original_step.get('Step No')}")
                st.markdown(f"**Test Steps:** {selected_original_step.get('Test Steps')}")
                st.markdown(f"**Expected Result:** {selected_original_step.get('Expected Result')}")

            # Stage 4b: UI Element Detection
            _handle_ui_element_detection(state, selected_step_table_entry, selected_original_step)

            # Stage 4c: Element Matching
            _handle_element_matching(state, selected_step_table_entry, selected_original_step)

def _handle_ui_element_detection(state, selected_step_table_entry, selected_original_step):
    """Handle UI element detection for the selected test case step."""
    st.subheader("4b. UI Element Detection")

    # Check if we have a step table analysis
    requires_ui_elements = True
    ui_element_reason = "UI element detection is needed for proper automation."

    if hasattr(state, 'step_table_analysis') and state.step_table_analysis:
        step_table_analysis = state.step_table_analysis
        requires_ui_elements = step_table_analysis.get("requires_ui_elements", True)
        ui_element_reason = step_table_analysis.get("reason", ui_element_reason)

    # Also check the specific step's requirements
    step_requires_ui_elements = selected_step_table_entry.get('locator_strategy') not in ["", "none", "n/a", "url"]

    # Display message about UI element detection requirement
    if requires_ui_elements and step_requires_ui_elements:
        st.info(f"🔍 **UI Element Detection Required**: {ui_element_reason}")

        # Show the Detect UI Elements and Interactive Selection buttons
        detect_col1, detect_col2 = st.columns(2)
        with detect_col1:
            detect_button = st.button(
                "🔍 Detect UI Elements Automatically",
                key="detect_ui_elements_btn",
                help="Automatically detect UI elements from the website for this step",
                use_container_width=True
            )

        with detect_col2:
            select_interactive_button = st.button(
                "👆 Select Element Interactively",
                key="select_element_interactive_btn",
                help="Open a browser window to manually select UI elements for this step",
                use_container_width=True
            )

        if select_interactive_button:
            _handle_interactive_element_selection(state, selected_step_table_entry, selected_original_step)

        if detect_button:
            _handle_automatic_element_detection(state, selected_step_table_entry)
    else:
        # If UI element detection is not needed, show a message and set empty elements
        st.success(f"✓ **UI Element Detection Not Needed**: {ui_element_reason}")

        # Create empty elements for the workflow to continue
        if not hasattr(state, 'detected_elements') or not state.detected_elements:
            state.detected_elements = []
            state.qa_relevant_elements = []
            st.info("Skipping UI element detection as it's not required for this step.")

def _handle_interactive_element_selection(state, selected_step_table_entry, selected_original_step):
    """Handle interactive element selection for the selected test case step."""
    with st.spinner("Opening browser for interactive element selection. Please select an element in the browser window..."):
        try:
            # Get locator strategy and value from the step table entry
            locator_strategy = None
            locator_value = None

            if selected_step_table_entry:
                locator_strategy = selected_step_table_entry.get('locator_strategy')
                locator_value = selected_step_table_entry.get('locator')

            # Launch interactive element selector
            website_url = state.website_url if hasattr(state, 'website_url') and state.website_url else "https://example.com"
            logger.info(f"Using website URL for interactive element selection: {website_url}")
            selected_element = select_element_interactively(website_url)

            if selected_element:
                # Convert the selected element to the format expected by the application
                element = {
                    'name': selected_element.get('tagName', '') + (f"#{selected_element.get('id')}" if selected_element.get('id') else ""),
                    'tag': selected_element.get('tagName', ''),
                    'selector': selected_element.get('cssSelector', ''),
                    'xpath': selected_element.get('xpath', ''),
                    'attributes': {
                        'id': selected_element.get('id', ''),
                        'name': selected_element.get('name', ''),
                        'class': selected_element.get('className', ''),
                        'type': selected_element.get('type', ''),
                        'value': selected_element.get('value', ''),
                        'placeholder': selected_element.get('placeholder', ''),
                        'href': selected_element.get('href', ''),
                        'role': selected_element.get('role', ''),
                        'aria-label': selected_element.get('ariaLabel', ''),
                        'text': selected_element.get('text', '')
                    },
                    'interactive': True,
                    'visible': True,
                    'manually_selected': True,
                    'score': 100  # Give manually selected elements the highest score
                }

                # Store the element in state manager
                if not hasattr(state, 'detected_elements'):
                    state.detected_elements = []

                # Add the manually selected element to the beginning of the list
                state.detected_elements.insert(0, element)

                # Apply QA-specific filtering (though we'll keep the manually selected element)
                qa_elements = [element]  # Start with the manually selected element

                # Add any other elements that match the filtering criteria
                if hasattr(state, 'detected_elements') and len(state.detected_elements) > 1:
                    other_qa_elements = filter_qa_relevant_elements(
                        state.detected_elements[1:],  # Skip the first element which is our manually selected one
                        locator_strategy=locator_strategy,
                        locator_value=locator_value
                    )
                    qa_elements.extend(other_qa_elements)

                state.qa_relevant_elements = qa_elements

                # Show success message with element information
                st.success(f"✓ Element selected: {element['name']} ({element['selector']})")

                # Show the selected element in an expander
                with st.expander("Selected Element Details", expanded=True):
                    st.json(element)

                # Automatically create element matches for the manually selected element
                # This allows skipping the element matching step (4c)
                _create_element_match_for_manual_selection(state, element, selected_step_table_entry, selected_original_step)
            else:
                st.warning("No element was selected or the selection timed out.")
        except Exception as e:
            st.error(f"Error during interactive element selection: {e}")
            import traceback
            st.error(traceback.format_exc())

def _create_element_match_for_manual_selection(state, element, selected_step_table_entry, selected_original_step):
    """Create element match for manually selected element for the selected test case step."""
    try:
        # Prepare the test case and step for automatic matching
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))

        # Create a match entry for the manually selected element
        element_match = {
            'element': element,
            'action': selected_step_table_entry.get('action', 'interact with'),
            'score': 1.0,  # Perfect match score
            'reasoning': 'This element was manually selected by the user.',
            'manually_selected': True
        }

        # Create the element matches structure
        if not hasattr(state, 'element_matches'):
            state.element_matches = {}

        if test_case_id not in state.element_matches:
            state.element_matches[test_case_id] = {}

        state.element_matches[test_case_id][step_no] = [element_match]
        state.step_matches = state.element_matches

        # Set the LLM step analysis
        # Analyze if the step requires test data
        test_data_analysis = analyze_step_for_test_data(
            selected_step_table_entry,
            selected_original_step.get('Test Steps')
        )

        # Store the complete analysis in session state
        state.llm_step_analysis = {
            "requires_ui_element": True,
            "reason": "Manually selected UI element will be used for this step.",
            "matches": [element_match],
            "requires_test_data": test_data_analysis["requires_test_data"],
            "test_data_reason": test_data_analysis["reason"],
            "data_types": test_data_analysis["data_types"]
        }

        # Store test data analysis separately for easier access
        state.test_data_analysis = test_data_analysis

        # Automatically set test_data_skipped flag for navigation steps
        if not test_data_analysis["requires_test_data"]:
            state.test_data_skipped = True
            state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists
            st.success("✓ Test data configuration automatically skipped for this navigation test case step.")
            st.info("You can proceed directly to Application Stage 6 to generate the test script.")

        # Inform the user that element matching is complete
        if not test_data_analysis["requires_test_data"]:
            st.info("✓ Element matching automatically completed with your manually selected element. You can proceed directly to Application Stage 6.")
        else:
            st.info("✓ Element matching automatically completed with your manually selected element. You can proceed to Application Stage 5.")
    except Exception as e:
        st.warning(f"Could not automatically complete element matching: {e}. Please use the 'Match Elements with Step' button in Application Stage 4c.")

def _handle_automatic_element_detection(state, selected_step_table_entry):
    """Handle automatic element detection for the selected test case step."""
    with st.spinner("Detecting and filtering UI elements from the website..."):
        try:
            # Get locator strategy and value from the step table entry
            locator_strategy = None
            locator_value = None

            if selected_step_table_entry:
                locator_strategy = selected_step_table_entry.get('locator_strategy')
                locator_value = selected_step_table_entry.get('locator')

                # Display the locator strategy being used
                if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', '']:
                    st.info(f"Using locator strategy: {locator_strategy}" +
                           (f" with value: {locator_value}" if locator_value else ""))

            # For step 1, extract from URL; for step 2+, extract from browser
            website_url = state.website_url if hasattr(state, 'website_url') and state.website_url else "https://example.com"
            logger.info(f"Using website URL for element detection: {website_url}")

            if hasattr(state, 'test_browser') and state.test_browser:
                # Use the live browser instance for extraction
                browser = state.test_browser
                elements = detect_elements(browser)  # This function should handle browser instance
            else:
                # First time: extract from URL with locator strategy
                elements = detect_elements_advanced(
                    website_url,
                    locator_strategy=locator_strategy,
                    locator_value=locator_value
                )
            state.detected_elements = elements

            # Apply QA-specific filtering to the detected elements using locator strategy
            qa_elements = filter_qa_relevant_elements(
                elements,
                locator_strategy=locator_strategy,
                locator_value=locator_value
            )
            state.qa_relevant_elements = qa_elements

            # Ensure step_elements is set for test data generation
            state.step_elements = qa_elements if qa_elements else elements

            # Show success message with locator strategy information
            if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', '']:
                st.success(f"✓ Detected {len(elements)} UI elements and filtered to {len(qa_elements)} QA-relevant elements using {locator_strategy} strategy.")
            else:
                st.success(f"✓ Detected {len(elements)} UI elements and filtered to {len(qa_elements)} QA-relevant elements.")

            # Show filtered QA-relevant UI elements in an expander
            with st.expander("Show QA-Relevant UI Elements", expanded=False):
                if qa_elements:
                    st.write(f"{len(qa_elements)} QA-relevant UI elements detected:")
                    st.json(qa_elements)
                else:
                    st.info("No QA-relevant UI elements detected.")
        except Exception as e:
            st.error(f"Error detecting elements: {e}")

def _handle_element_matching(state, selected_step_table_entry, selected_original_step):
    """Handle element matching for the selected test case step."""
    st.subheader("4c. Element Matching")

    # Check if we already have a manually selected element with matches
    has_manual_selection = False
    if (hasattr(state, 'element_matches') and
        state.element_matches and
        any(match.get('manually_selected', False)
            for matches in state.element_matches.values()
            for step_matches in matches.values()
            for match in step_matches)):
        has_manual_selection = True
        st.success("✓ You've manually selected an element for this step. No additional matching is needed.")

        # Show the manually selected element
        if hasattr(state, 'detected_elements') and state.detected_elements:
            for element in state.detected_elements:
                if element.get('manually_selected', False):
                    with st.expander("Your Selected Element", expanded=False):
                        st.json(element)
                    break

    # Check if this is a navigation step that doesn't require UI elements
    elif not hasattr(state, 'step_table_analysis') or not state.step_table_analysis.get("requires_ui_elements", True) or selected_step_table_entry.get('locator_strategy') in ["", "none", "n/a", "url"]:
        # Automatically complete the element matching for navigation steps
        if not hasattr(state, 'element_matches'):
            state.element_matches = {}

        # Create empty elements list for navigation steps
        if not hasattr(state, 'step_elements'):
            state.step_elements = []

        # Get test case ID and step number
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))

        # Create an empty match for navigation steps
        if test_case_id not in state.element_matches:
            state.element_matches[test_case_id] = {}

        # Create an empty match list for this step
        state.element_matches[test_case_id][step_no] = []
        state.step_matches = state.element_matches

        # Analyze if the step requires test data
        test_data_analysis = analyze_step_for_test_data(
            selected_step_table_entry,
            selected_original_step.get('Test Steps')
        )

        # Store the analysis in session state
        state.llm_step_analysis = {
            "requires_ui_element": False,
            "reason": "This is a navigation step that doesn't require UI elements.",
            "matches": [],
            "requires_test_data": test_data_analysis["requires_test_data"],
            "test_data_reason": test_data_analysis["reason"],
            "data_types": test_data_analysis["data_types"]
        }

        # Store test data analysis separately for easier access
        state.test_data_analysis = test_data_analysis

        # Automatically set test_data_skipped flag for navigation steps
        if not test_data_analysis["requires_test_data"]:
            state.test_data_skipped = True
            state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists

        st.success("✓ Element matching automatically completed for this navigation test case step.")
        st.info("Navigation test case steps don't require UI element detection or matching.")

        # Add guidance for next steps
        if not test_data_analysis["requires_test_data"]:
            st.info("You can proceed directly to Application Stage 6 to generate the test script.")
        else:
            st.info("You can proceed to Application Stage 5 to configure test data.")

    # Check if element detection is required but not done
    elif hasattr(state, 'step_table_analysis') and state.step_table_analysis.get("requires_ui_elements", True) and selected_step_table_entry.get('locator_strategy') not in ["", "none", "n/a", "url"] and not hasattr(state, 'detected_elements'):
        st.warning("⚠️ Please detect UI elements first using the 'Detect UI Elements' button above or select an element interactively")
    else:
        # Prepare for element matching
        if hasattr(state, 'qa_relevant_elements') and state.qa_relevant_elements:
            elements_for_matching = state.qa_relevant_elements
            element_count = len(elements_for_matching)
            st.info(f"Using {element_count} QA-relevant elements for matching")
        elif hasattr(state, 'detected_elements') and state.detected_elements:
            elements_for_matching = state.detected_elements
            element_count = len(elements_for_matching)
            st.info(f"Using {element_count} detected elements for matching")
        else:
            st.warning("⚠️ No elements detected. Please detect UI elements first.")
            return

        state.step_elements = elements_for_matching

        # Button to analyze the selected step with detected elements
        # Disable the button if UI elements are required but not detected or if we already have a manual selection
        button_disabled = (hasattr(state, 'step_table_analysis') and
                          state.step_table_analysis.get("requires_ui_elements", True) and
                          selected_step_table_entry.get('locator_strategy') not in ["", "none", "n/a", "url"] and
                          not hasattr(state, 'detected_elements')) or has_manual_selection

        # Use a single column for the match button to make it more prominent
        match_button = st.button(
            "🔄 Match Elements with Step",
            disabled=button_disabled,
            key="match_elements_btn",
            help="Match UI elements with the selected step" if not has_manual_selection else "Not needed - you've already selected an element manually",
            use_container_width=True
        )

        if match_button:
            _perform_element_matching(state, selected_step_table_entry, selected_original_step, elements_for_matching)

def _perform_element_matching(state, selected_step_table_entry, selected_original_step, elements_for_matching):
    """Perform element matching using AI for the selected test case step."""
    if hasattr(state, 'step_table_analysis') and state.step_table_analysis.get("requires_ui_elements", True) and selected_step_table_entry.get('locator_strategy') not in ["", "none", "n/a", "url"] and not hasattr(state, 'detected_elements'):
        st.error("Please detect UI elements first using the 'Detect UI Elements' button above")
    else:
        with st.spinner("Analyzing test case step and matching elements..."):
            try:
                # Prepare the test case and step for analysis
                # Use both the original step and the step table entry
                test_case_for_analysis = {
                    "id": state.selected_test_case.get('Test Case ID'),
                    "objective": state.selected_test_case.get('Test Case Objective'),
                    "steps": [{
                        "step_no": str(selected_original_step.get('Step No')),
                        "action": selected_original_step.get('Test Steps'),
                        "expected": selected_original_step.get('Expected Result'),
                        # Add step table information
                        "step_type": selected_step_table_entry.get('step_type'),
                        "locator_strategy": selected_step_table_entry.get('locator_strategy'),
                        "locator": selected_step_table_entry.get('locator'),
                        "test_data_param": selected_step_table_entry.get('test_data_param'),
                        "assertion_type": selected_step_table_entry.get('assertion_type')
                    }]
                }

                # Match elements with the selected step using context-aware analysis
                use_ai_matching = True  # This should be replaced with the actual value from state

                # Check if match_elements_with_ai function is available
                if 'match_elements_with_ai' in globals() and callable(match_elements_with_ai) and use_ai_matching:
                    logger.info("Using AI-powered element matching")
                    element_matches = match_elements_with_ai(
                        test_case_for_analysis,
                        elements_for_matching,
                        state.google_api_key
                    )
                else:
                    st.error("AI element matching function is not available. Please check the core.element_matching module.")
                    element_matches = {}

                    if not element_matches or not isinstance(element_matches, dict):
                        st.warning("No valid element matches returned from analysis.")
                        state.step_matches = {}
                        state.element_matches = {}
                        state.llm_step_analysis = {}
                    else:
                        # Store matches in state manager
                        state.step_matches = element_matches
                        state.element_matches = element_matches
                        st.success("✓ Element matching completed")

                        # Extract LLM step analysis info from the result
                        tc_id = test_case_for_analysis["id"]
                        step_no = test_case_for_analysis["steps"][0]["step_no"]
                        step_result = element_matches.get(tc_id, {}).get(str(step_no), [])
                        requires_ui_element = bool(step_result)

                        # Analyze if the step requires test data
                        test_data_analysis = analyze_step_for_test_data(
                            selected_step_table_entry,
                            selected_original_step.get('Test Steps')
                        )

                        # Store the complete analysis in state manager
                        state.llm_step_analysis = {
                            "requires_ui_element": requires_ui_element,
                            "reason": "Matched UI element(s) found." if requires_ui_element else "No relevant UI element required for this step.",
                            "matches": step_result,
                            "requires_test_data": test_data_analysis["requires_test_data"],
                            "test_data_reason": test_data_analysis["reason"],
                            "data_types": test_data_analysis["data_types"]
                        }

                        # Store test data analysis separately for easier access
                        state.test_data_analysis = test_data_analysis

                        # Automatically set test_data_skipped flag for navigation steps
                        if not test_data_analysis["requires_test_data"]:
                            state.test_data_skipped = True
                            state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists
                            state.step_ready_for_script = True  # Mark the step as ready for script generation
                            st.success("✓ Test data configuration automatically skipped for this navigation test case step.")
                            st.info("You can proceed directly to Application Stage 6 to generate the test script.")

                        # Display matches
                        _display_element_matches(state, tc_id, step_no, step_result, selected_step_table_entry)
            except Exception as e:
                st.error(f"Error during LLM analysis or element matching: {e}")

def _display_previous_step_context(state, selected_step):
    """
    Display context from previous steps to maintain continuity in the workflow.

    This function shows information from previously completed steps that might be
    relevant to the current step, such as elements detected, test data used, and
    scripts generated. This helps maintain context as the user progresses through
    the test case steps.

    Args:
        state (StateManager): The application state manager instance
        selected_step (dict): The currently selected test step
    """
    # Check if we have any completed steps and step context
    if (hasattr(state, 'completed_steps') and state.completed_steps and
        hasattr(state, 'step_context') and state.step_context):

        # Get the current step number
        current_step_no = str(selected_step.get('Step No'))

        # Find previous steps that have been completed
        previous_steps = [step for step in state.completed_steps if step != current_step_no]

        if previous_steps:
            with st.expander("View Context from Previous Steps", expanded=False):
                st.markdown("### Previous Steps Context")

                # Create tabs for each previous step
                if len(previous_steps) > 1:
                    tabs = st.tabs([f"Step {step}" for step in previous_steps])

                    for i, step_no in enumerate(previous_steps):
                        with tabs[i]:
                            if step_no in state.step_context:
                                step_ctx = state.step_context[step_no]

                                # Show elements if available
                                if step_ctx.get("elements"):
                                    st.markdown(f"**UI Elements:** {len(step_ctx['elements'])} elements detected")

                                # Show test data if available
                                if step_ctx.get("test_data"):
                                    st.markdown("**Test Data Used:**")
                                    for key, value in step_ctx["test_data"].items():
                                        st.markdown(f"- {key}: `{value}`")

                                # Show script path if available
                                if step_ctx.get("script_path"):
                                    st.markdown(f"**Generated Script:** {os.path.basename(step_ctx['script_path'])}")
                            else:
                                st.info(f"No context available for Step {step_no}")
                else:
                    # Just one previous step
                    step_no = previous_steps[0]
                    if step_no in state.step_context:
                        step_ctx = state.step_context[step_no]

                        # Show elements if available
                        if step_ctx.get("elements"):
                            st.markdown(f"**UI Elements:** {len(step_ctx['elements'])} elements detected")

                        # Show test data if available
                        if step_ctx.get("test_data"):
                            st.markdown("**Test Data Used:**")
                            for key, value in step_ctx["test_data"].items():
                                st.markdown(f"- {key}: `{value}`")

                        # Show script path if available
                        if step_ctx.get("script_path"):
                            st.markdown(f"**Generated Script:** {os.path.basename(step_ctx['script_path'])}")
                    else:
                        st.info(f"No context available for Step {step_no}")

def _display_element_matches(state, tc_id, step_no, step_result, selected_step_table_entry):
    """
    Display element matches in an organized and user-friendly format.

    Creates an expandable section that shows the matched UI elements for a specific
    test case step, including confidence scores, reasoning, and locator strategy matches.

    Args:
        state (StateManager): The application state manager instance (unused but kept for consistency)
        tc_id (str): The test case ID
        step_no (str): The step number
        step_result (list): List of element matches with their details
        selected_step_table_entry (dict): The selected step table entry with locator information
    """
    with st.expander("View Element Matches", expanded=True):
        st.markdown(f"### Test Case: {tc_id}")
        st.markdown(f"**Step {step_no}:**")

        # Get locator strategy from step table entry
        locator_strategy = None
        locator_value = None
        if selected_step_table_entry:
            locator_strategy = selected_step_table_entry.get('locator_strategy')
            locator_value = selected_step_table_entry.get('locator')

        if step_result:
            for match in step_result:
                element = match.get('element', {})
                element_name = element.get('name', 'Unknown')
                selector = element.get('selector', 'Unknown')
                action = match.get('action', 'Unknown')

                # Check if element matches the locator strategy
                locator_match = False
                if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', '']:
                    attrs = element.get('attributes', {})

                    # Map locator strategies to element attributes
                    strategy_to_attribute = {
                        'id': 'id',
                        'name': 'name',
                        'css': 'selector',
                        'xpath': 'xpath',
                        'tag': 'tag',
                        'class': 'class',
                        'link_text': 'text',
                        'partial_link_text': 'text',
                        'aria': 'role'
                    }

                    attribute = strategy_to_attribute.get(locator_strategy.lower())
                    if attribute:
                        if attribute == 'selector':
                            locator_match = True
                        elif attribute == 'xpath':
                            locator_match = True
                        else:
                            attr_value = attrs.get(attribute, '')
                            if attr_value:
                                locator_match = True
                                if locator_value:
                                    if locator_strategy.lower() == 'partial_link_text':
                                        locator_match = locator_value.lower() in attr_value.lower()
                                    else:
                                        locator_match = attr_value.lower() == locator_value.lower()

                # Display element with locator match indicator
                if locator_match:
                    st.markdown(f"- Element: **{element_name}** ({selector}) ✅ *Matches {locator_strategy}*")
                else:
                    st.markdown(f"- Element: **{element_name}** ({selector})")

                st.markdown(f"  - Action: {action}")

                # Show confidence score if available
                if 'score' in match:
                    score = match.get('score', 0)
                    st.progress(score)
                    st.markdown(f"  - Confidence: {score:.2f}")

                # Show reasoning if available
                if 'reasoning' in match:
                    st.markdown(f"  - Reasoning: {match.get('reasoning')}")

                # Show locator strategy match information
                if element.get('locator_strategy_match'):
                    st.markdown(f"  - Locator Strategy Match: ✅ *Matches {locator_strategy}*")
                elif locator_strategy and locator_strategy.lower() not in ['none', 'n/a', '']:
                    st.markdown(f"  - Locator Strategy Match: ❌ *Does not match {locator_strategy}*")
        else:
            st.info("No UI element required for this step.")

        # Show warning if no elements match the locator strategy
        if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', ''] and step_result:
            matches_locator = any(
                match.get('element', {}).get('locator_strategy_match', False)
                for match in step_result
            )
            if not matches_locator:
                st.warning(f"⚠️ No elements match the specified locator strategy: {locator_strategy}")
                st.markdown("Try a different locator strategy or use CSS/XPath.")

def stage5_test_data(state):
    """
    Application Stage 5: Configure Test Data for Selected Test Case Step.

    This stage allows the user to configure test data for the selected test case step.
    It analyzes the step to determine if test data is required, and if so,
    provides a UI for generating and configuring test data. If test data is
    not required (e.g., for navigation steps), it allows skipping to the next stage.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>Application Stage 5: Configure Test Data for Selected Test Case Step</h2>", unsafe_allow_html=True)

    # Check if we have a selected step and matched elements
    llm_step_analysis = getattr(state, 'llm_step_analysis', {})
    test_data_skipped = getattr(state, 'test_data_skipped', False)

    # Ensure step_elements is available for test data generation
    if hasattr(state, 'selected_step') and state.selected_step:
        if not hasattr(state, 'step_elements') or not state.step_elements:
            # Fallback: use detected_elements or qa_relevant_elements if available
            if hasattr(state, 'qa_relevant_elements') and state.qa_relevant_elements:
                state.step_elements = state.qa_relevant_elements
            elif hasattr(state, 'detected_elements') and state.detected_elements:
                state.step_elements = state.detected_elements
            else:
                # Create empty list as last resort
                state.step_elements = []

    # Check if test data has already been automatically skipped
    if test_data_skipped and not llm_step_analysis.get("requires_test_data", True):
        st.success("✓ Test data configuration has been automatically skipped for this navigation step")
        st.info(f"Reason: {llm_step_analysis.get('test_data_reason', 'No test data is needed for this step type.')}")

        # Add a visual indicator
        st.markdown("""
        <div style="text-align: center; margin-top: 10px; background-color: #e8f5e9; padding: 5px; border-radius: 5px; border: 1px solid #4CAF50;">
            <span style="color: #4CAF50;">⬇️ Proceed to Application Stage 6 below</span>
        </div>
        """, unsafe_allow_html=True)
    # Check if prerequisites are met
    elif not hasattr(state, 'selected_step') or not hasattr(state, 'step_matches'):
        st.warning("⚠️ Please select a test case step and analyze it in Application Stage 4 first")
    # Check if test data is required based on analysis
    elif not llm_step_analysis.get("requires_test_data", True):
        # Display a clear message explaining why test data is not needed
        st.success(f"✓ This test case step does not require test data configuration")
        st.info(f"Reason: {llm_step_analysis.get('test_data_reason', 'No test data is needed for this step type.')}")

        # Provide a button to skip directly to script generation
        if st.button("Skip to Application Stage 6 (Script Generation)", key="skip_to_script_gen"):
            # Set a flag in state manager to indicate test data was skipped but it's OK
            state.test_data_skipped = True
            state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists
            state.step_ready_for_script = True  # Mark the step as ready for script generation
            # Display a success message and instructions
            st.success("✓ Test data configuration skipped. You can now proceed to Application Stage 6.")
            # Add a visual indicator
            st.markdown("""
            <div style="text-align: center; margin-top: 10px;">
                <span style="color: #4CAF50;">⬇️ Proceed to Application Stage 6 below</span>
            </div>
            """, unsafe_allow_html=True)
            st.rerun()  # Refresh the page to update the UI
    else:
        # Test data is required - show the configuration UI
        # --- Show explicit message if test data is required ---
        st.warning(f"This step requires test data configuration: {llm_step_analysis.get('test_data_reason', 'Data input is needed for this step.')}")

        # Add a button to force skip test data configuration (for cases where state tracking has issues)
        col1, col2 = st.columns([1, 2])
        with col1:
            if st.button("Skip Test Data Configuration and Proceed to Application Stage 6", key="force_skip_test_data"):
                # Set a flag in state manager to indicate test data was skipped
                state.test_data_skipped = True
                state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists
                state.step_ready_for_script = True  # Mark the step as ready for script generation
                # Display a success message and instructions
                st.success("✓ Test data configuration skipped. You can now proceed to Application Stage 6.")
                # Add a visual indicator
                st.markdown("""
                <div style="text-align: center; margin-top: 10px;">
                    <span style="color: #4CAF50;">⬇️ Proceed to Application Stage 6 below</span>
                </div>
                """, unsafe_allow_html=True)
                st.rerun()  # Refresh the page to update the UI

        with col2:
            st.info("Use 'Skip Test Data Configuration and Proceed to Application Stage 6' if needed.")

        # If we have specific data types identified, show them
        data_types = llm_step_analysis.get('data_types', [])
        if data_types:
            st.markdown("**Detected data types needed:**")
            for data_type in data_types:
                st.markdown(f"- {data_type}")

        # Simple header for test data generation
        st.markdown("**Smart Test Data Generation**")

        # Add a button to generate test data for the selected step
        if st.button("Generate Test Data for Selected Test Case Step"):
            selected_step = state.selected_step
            step_elements = state.step_elements

            if not selected_step:
                st.error("Please select a test case step in Application Stage 4 first.")
            elif not step_elements and not hasattr(state, 'detected_elements') and not hasattr(state, 'qa_relevant_elements'):
                st.error("Please detect UI elements for the selected test case step in Application Stage 4 first.")
            else:
                # Ensure step_elements is available (use detected_elements as fallback)
                if not step_elements:
                    if hasattr(state, 'qa_relevant_elements') and state.qa_relevant_elements:
                        step_elements = state.qa_relevant_elements
                    elif hasattr(state, 'detected_elements') and state.detected_elements:
                        step_elements = state.detected_elements
                    else:
                        step_elements = []
                    # Update session state
                    state.step_elements = step_elements

                with st.spinner("Generating test data for selected step..."):
                    try:
                        from test_data_manager import generate_test_data_for_step

                        # Prepare step data for test data generation
                        step_data = {
                            "action": selected_step.get('Test Steps', ''),
                            "expected": selected_step.get('Expected Result', '')
                        }

                        # Get step table entry if available
                        step_table_entry = None
                        if hasattr(state, 'selected_step_table_entry') and state.selected_step_table_entry:
                            step_table_entry = state.selected_step_table_entry

                        # Generate test data for the specific step, including step table information
                        step_test_data = generate_test_data_for_step(step_data, step_elements, step_table_entry)

                        # Store in state manager
                        if not hasattr(state, 'test_data') or not isinstance(state.test_data, dict):
                            state.test_data = {}

                        # Update state manager with step-specific test data
                        state.test_data.update(step_test_data)
                        state.step_ready_for_script = True  # Mark the step as ready for script generation

                        # Display the generated test data
                        st.success(f"Generated test data for test case step {selected_step.get('Step No')}")

                        _display_generated_test_data(state, step_test_data)
                    except Exception as e:
                        st.error(f"Error generating test data: {e}")

def _display_generated_test_data(state, step_test_data):
    """
    Display the generated test data in a visually appealing way.

    Creates an expandable section that shows the test data organized by categories
    (user data, contact data, payment data, etc.) in a user-friendly format.

    Args:
        state (StateManager): The application state manager instance (unused but kept for consistency)
        step_test_data (dict): Dictionary containing the generated test data for the test case step
    """
    with st.expander("View Generated Test Data for Test Case Step", expanded=True):
        # Display the test data in a more organized and visually appealing way
        st.markdown("### Generated Test Data")

        # Group test data by categories for better organization
        user_data = {}
        contact_data = {}
        payment_data = {}
        form_data = {}
        other_data = {}

        # Categorize the test data
        for key, value in step_test_data.items():
            key_lower = key.lower()
            if any(word in key_lower for word in ['email', 'user', 'name', 'password', 'login']):
                user_data[key] = value
            elif any(word in key_lower for word in ['phone', 'mobile', 'address', 'city', 'state', 'zip', 'country']):
                contact_data[key] = value
            elif any(word in key_lower for word in ['card', 'payment', 'cvv', 'expiry', 'credit']):
                payment_data[key] = value
            elif any(word in key_lower for word in ['input', 'field', 'form', 'text', 'select']):
                form_data[key] = value
            else:
                other_data[key] = value

        # Display data in a more visually appealing way with columns if there's enough data
        if len(step_test_data) > 5:
            col1, col2 = st.columns(2)

            # Column 1: User and Contact data
            with col1:
                if user_data:
                    st.markdown("**👤 User Information**")
                    for key, value in user_data.items():
                        st.markdown(f"**{key}:** `{value}`")
                    st.markdown("---")

                if contact_data:
                    st.markdown("**📱 Contact Information**")
                    for key, value in contact_data.items():
                        st.markdown(f"**{key}:** `{value}`")

            # Column 2: Payment and Other data
            with col2:
                if payment_data:
                    st.markdown("**💳 Payment Information**")
                    for key, value in payment_data.items():
                        st.markdown(f"**{key}:** `{value}`")
                    st.markdown("---")

                if form_data:
                    st.markdown("**📝 Form Data**")
                    for key, value in form_data.items():
                        st.markdown(f"**{key}:** `{value}`")

            # Display other data below the columns
            if other_data:
                st.markdown("**🔄 Other Test Data**")
                for key, value in other_data.items():
                    st.markdown(f"**{key}:** `{value}`")
        else:
            # For fewer items, display in a single column
            for key, value in step_test_data.items():
                st.markdown(f"**{key}:** `{value}`")

def stage6_generate_script(state):
    """
    Application Stage 6: Generate Test Script for Selected Test Case Step.

    This stage allows the user to generate a test script for the selected test case step.
    It checks if all prerequisites are met (step selection, element matching, test data),
    displays the selected step information, and provides options for script generation.
    When the user clicks the generate button, it calls the AI to generate a test script
    and displays the result.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>Application Stage 6: Generate Test Script for Selected Test Case Step</h2>", unsafe_allow_html=True)

    # Check if we have a selected step
    if not hasattr(state, 'selected_step') or not state.selected_step:
        st.warning("⚠️ Please select a test case step in Application Stage 4 first")
        return

    # Check if element matching is complete
    if not hasattr(state, 'step_matches') and not hasattr(state, 'element_matches'):
        st.warning("⚠️ Please complete element matching in Application Stage 4 first")
        return

    # Check if test data is required but not configured
    llm_step_analysis = getattr(state, 'llm_step_analysis', {})
    test_data_skipped = getattr(state, 'test_data_skipped', False)

    if llm_step_analysis.get("requires_test_data", False) and not test_data_skipped and not hasattr(state, 'test_data'):
        st.warning("⚠️ Please configure test data in Application Stage 5 first")
        return

    # Get the selected step information
    selected_step = state.selected_step
    selected_step_table_entry = getattr(state, 'selected_step_table_entry', None)

    # Display step information
    st.markdown("### Selected Step Information")
    col1, col2 = st.columns(2)
    with col1:
        st.markdown(f"**Step Number:** {selected_step.get('Step No')}")
        st.markdown(f"**Action:** {selected_step.get('Test Steps')}")
    with col2:
        st.markdown(f"**Expected Result:** {selected_step.get('Expected Result')}")

    # Display element matches if available
    if hasattr(state, 'element_matches') and state.element_matches:
        # Get the test case ID and step number
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_step.get('Step No'))

        # Get the matches for this specific step
        step_matches = state.element_matches.get(test_case_id, {}).get(step_no, [])

        if step_matches:
            with st.expander("View Element Matches", expanded=False):
                for i, match in enumerate(step_matches):
                    element = match.get('element', {})
                    element_name = element.get('name', 'Unknown')
                    selector = element.get('selector', 'Unknown')
                    action = match.get('action', 'Unknown')

                    st.markdown(f"**Match {i+1}:** {element_name} ({selector})")
                    st.markdown(f"- Action: {action}")

                    # Show confidence score if available
                    if 'score' in match:
                        score = match.get('score', 0)
                        st.progress(score)
                        st.markdown(f"- Confidence: {score:.2f}")

    # Display test data if available
    if hasattr(state, 'test_data') and state.test_data:
        with st.expander("View Test Data", expanded=False):
            st.markdown("### Test Data")
            for key, value in state.test_data.items():
                st.markdown(f"**{key}:** `{value}`")

    # Script generation heading
    st.markdown("### Script Generation")

    # Add a button to generate the test script
    if st.button("Generate Test Script for Test Case Step", key="generate_script_btn", use_container_width=True):
        with st.spinner("Generating test script for selected test case step..."):
            try:
                # Import the necessary function
                from core.ai import generate_test_script

                # Prepare the inputs for script generation
                test_case = state.selected_test_case
                step = selected_step
                step_table_entry = selected_step_table_entry

                # Get element matches for this step
                element_matches = []
                if hasattr(state, 'element_matches') and state.element_matches:
                    test_case_id = test_case.get('Test Case ID')
                    step_no = str(step.get('Step No'))
                    element_matches = state.element_matches.get(test_case_id, {}).get(step_no, [])

                # Get test data for this step
                test_data = {}
                if hasattr(state, 'test_data') and state.test_data:
                    test_data = state.test_data

                # We'll generate the script directly without a separate prompt step

                # Generate the test script - now returns a tuple (merged_script, step_specific_script)
                script_result = generate_test_script(
                    test_case,
                    element_matches,
                    test_data,
                    state.website_url,
                    step_table_entry,
                    state.google_api_key,
                    state  # Pass the state for script continuity
                )

                # Unpack the result - handle both the new tuple return value and legacy string return for backward compatibility
                if isinstance(script_result, tuple):
                    merged_script, step_specific_script = script_result
                    logger.info(f"Received both merged script ({len(merged_script)} chars) and step-specific script ({len(step_specific_script)} chars)")
                else:
                    # For backward compatibility with older versions
                    merged_script = script_result
                    step_specific_script = None
                    logger.info(f"Received only merged script ({len(merged_script)} chars) - using legacy mode")

                # Use the merged script as the main script content
                script_content = merged_script

                # Check for errors before proceeding
                if script_content.startswith("Error"):
                    st.error(script_content)
                else:
                    # Create file paths for both scripts with timestamp for versioning
                    script_dir = "generated_tests"
                    os.makedirs(script_dir, exist_ok=True)

                    # Create timestamp for consistent filenames
                    timestamp = int(time.time())
                    test_case_id = test_case.get('Test Case ID', 'unknown')
                    step_no = step.get('Step No', '0')

                    # Path for the merged script
                    script_file = os.path.join(
                        script_dir,
                        f"test_{test_case_id}_{step_no}_{timestamp}_merged.py"
                    )

                    # Save the merged script to a file
                    with open(script_file, "w") as f:
                        f.write(script_content)

                    # If we have a step-specific script, save it too
                    step_specific_file = None
                    if step_specific_script:
                        step_specific_file = os.path.join(
                            script_dir,
                            f"test_{test_case_id}_{step_no}_{timestamp}_step_only.py"
                        )
                        with open(step_specific_file, "w") as f:
                            f.write(step_specific_script)
                        logger.info(f"Saved step-specific script to {step_specific_file}")

                if script_content and not script_content.startswith("Error") and 'script_file' in locals():
                    # Store the script content and file path in state manager
                    # Only log state changes when values actually change
                    if not hasattr(state, 'last_script_content') or state.last_script_content != script_content:
                        state.last_script_content = script_content
                        logger.info("State change: updated last_script_content")

                    # Store the step-specific script if available
                    if step_specific_script and (not hasattr(state, 'last_step_specific_script') or state.last_step_specific_script != step_specific_script):
                        state.last_step_specific_script = step_specific_script
                        logger.info("State change: updated last_step_specific_script")

                    if step_specific_file and (not hasattr(state, 'last_step_specific_file') or state.last_step_specific_file != step_specific_file):
                        state.last_step_specific_file = step_specific_file
                        logger.info(f"State change: last_step_specific_file = {step_specific_file}")

                    if not hasattr(state, 'last_script_file') or state.last_script_file != script_file:
                        state.last_script_file = script_file
                        logger.info(f"State change: last_script_file = {script_file}")

                    if not hasattr(state, 'generated_script_path') or state.generated_script_path != script_file:
                        state.generated_script_path = script_file
                        logger.info(f"State change: generated_script_path = {script_file}")

                    if not hasattr(state, 'script_just_generated') or not state.script_just_generated:
                        state.script_just_generated = True
                        logger.info("State change: script_just_generated = True")

                    if not hasattr(state, 'step_ready_for_script') or not state.step_ready_for_script:
                        state.step_ready_for_script = True
                        logger.info("State change: step_ready_for_script = True")

                    # Display the generated script
                    st.success(f"✅ Test script generated successfully: {os.path.basename(script_file)}")

                    # Make the script display collapsible but expanded by default
                    with st.expander("Generated Python Test Script (Merged)", expanded=True):
                        # Add utility buttons for the merged script
                        col1, col2 = st.columns([1, 1])
                        with col1:
                            if st.button("📋 Copy Merged Script", key="copy_merged_script"):
                                st.session_state['clipboard_content'] = script_content
                                st.success("✅ Merged script copied to clipboard! Use Ctrl+V to paste.")
                        with col2:
                            if st.download_button(
                                label="💾 Download Merged Script",
                                data=script_content,
                                file_name=os.path.basename(script_file),
                                mime="text/plain",
                                key="download_merged_script"
                            ):
                                st.success("✅ Merged script downloaded successfully!")

                        # Display the script with syntax highlighting
                        st.code(script_content, language="python")
                        st.info(f"Merged script file: {script_file}")

                    # If we have a step-specific script, display it in a separate expander
                    if step_specific_script and step_specific_file:
                        with st.expander("Step-Specific Python Test Script", expanded=False):
                            # Add utility buttons for the step-specific script
                            col1, col2 = st.columns([1, 1])
                            with col1:
                                if st.button("📋 Copy Step Script", key="copy_step_script"):
                                    st.session_state['clipboard_content'] = step_specific_script
                                    st.success("✅ Step-specific script copied to clipboard! Use Ctrl+V to paste.")
                            with col2:
                                if st.download_button(
                                    label="💾 Download Step Script",
                                    data=step_specific_script,
                                    file_name=os.path.basename(step_specific_file),
                                    mime="text/plain",
                                    key="download_step_script"
                                ):
                                    st.success("✅ Step-specific script downloaded successfully!")

                            # Display the script with syntax highlighting
                            st.code(step_specific_script, language="python")
                            st.info(f"Step-specific script file: {step_specific_file}")
                            st.info("This script contains only the code for the current step, without merging with previous steps.")

                    # Add a diff view to highlight differences between scripts
                    if step_specific_script and script_content:
                        with st.expander("Script Comparison (Diff View)", expanded=False):
                            st.info("This view highlights the differences between the step-specific script and the merged script.")

                            # Generate the HTML diff
                            html_diff = generate_html_diff(step_specific_script, script_content)

                            # Display the HTML diff
                            st.components.v1.html(html_diff, height=600, scrolling=True)

                    # Add a color-coded view to show script origins
                    if step_specific_script and script_content:
                        with st.expander("Script Origins (Color-Coded View)", expanded=False):
                            st.info("This view shows which parts of the merged script came from previous steps vs. the current step.")
                            st.markdown("""
                            <div style="margin-bottom: 10px;">
                                <span style="background-color: #e6f3ff; padding: 2px 5px; border-radius: 3px;">■ Blue</span> - Code from current step
                                <span style="background-color: #e6ffe6; padding: 2px 5px; border-radius: 3px; margin-left: 10px;">■ Green</span> - Code from previous steps
                            </div>
                            """, unsafe_allow_html=True)

                            # Analyze script origins
                            origins = analyze_script_origins(step_specific_script, script_content)

                            # Generate color-coded HTML
                            colored_html = generate_colored_html_script(script_content, origins)

                            # Display the color-coded HTML
                            st.components.v1.html(colored_html, height=600, scrolling=True)

                    # Add a visual indicator to proceed to Stage 7
                    st.markdown("""
                    <div style="text-align: center; margin-top: 10px; background-color: #e8f5e9; padding: 5px; border-radius: 5px; border: 1px solid #4CAF50;">
                        <span style="color: #4CAF50;">⬇️ Proceed to Application Stage 7 below to run the test script</span>
                    </div>
                    """, unsafe_allow_html=True)

                    # No step progression elements in Stage 6 - they will only appear after Stage 7 (Run Script)
                else:
                    if not script_content:
                        st.error("AI script generation returned empty content.")
                    elif script_content.startswith("Error"):
                        # Error already displayed above
                        pass
                    else:
                        st.error("Failed to save the generated script. Please check the logs for details.")
            except Exception as e:
                st.error(f"Error generating test script: {e}")
                import traceback
                st.error(traceback.format_exc())

def stage7_run_script(state):
    """
    Application Stage 7: Run Test Script for Selected Test Case Step.

    This stage allows the user to run the generated test script for the selected test case step.
    It checks if a script has been generated, displays the script, and provides a simple button
    to run the test. When the user clicks the button, it executes the script using pytest and
    displays the results, including any screenshots captured during the test run.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>Application Stage 7: Run Test Script</h2>", unsafe_allow_html=True)

    if not hasattr(state, 'generated_script_path') or not state.generated_script_path:
        st.warning("⚠️ Please generate a test script in Stage 6 first")
        return

    # Create two columns for script info and test status
    col1, col2 = st.columns([3, 2])

    with col1:
        st.success(f"✓ Script ready: {os.path.basename(state.generated_script_path)}")

    with col2:
        # Show test case step info
        step_no = state.selected_step.get('Step No', 'Unknown')
        st.info(f"Test Case Step: {step_no}")

    # Display the generated script in a collapsible section
    if hasattr(state, 'last_script_content') and state.last_script_content:
        with st.expander("View Test Script", expanded=False):
            st.code(state.last_script_content, language="python")
            if hasattr(state, 'last_script_file') and state.last_script_file:
                st.info(f"Script file: {state.last_script_file}")

    # Add a visual indicator to encourage running the script
    st.markdown("""
    <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: #e8f5e9; border-radius: 10px; border: 1px solid #4CAF50;">
        <p style="font-size: 16px; color: #4CAF50; margin: 0;">Click the button below to run the test script</p>
        <p style="font-size: 14px; color: #4CAF50; margin: 5px 0 0 0;">This will open a browser window to execute the test</p>
    </div>
    """, unsafe_allow_html=True)

    # Simplified run button without options
    if st.button("Run Test Script", disabled=not state.generated_script_path, use_container_width=True):
        with st.spinner(f"Running test script for Step {state.selected_step.get('Step No')}..."):
            try:
                # Set environment variables for the test run
                env = os.environ.copy()
                # Always run in visible mode (not headless)
                env["HEADLESS"] = "0"

                # Run the test script
                import subprocess
                result = subprocess.run(
                    ["pytest", state.generated_script_path, "--tb=short", "-v"],
                    capture_output=True, text=True,
                    env=env
                )

                # Store the test results
                state.test_results = {
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "returncode": result.returncode,
                    "step_no": state.selected_step.get('Step No'),
                    "test_case_id": state.selected_test_case.get('Test Case ID')
                }

                # Display the test results
                if result.returncode == 0:
                    st.success(f"Test Case Step {state.selected_step.get('Step No')} test passed!")

                    # Set the step_ready_for_script flag to True after successful test execution
                    if not state.step_ready_for_script:
                        state.step_ready_for_script = True
                        logger.info("State change: step_ready_for_script = True (after successful test execution)")

                    # Force state update in session state
                    st.session_state['state'] = state
                else:
                    st.error(f"Test Case Step {state.selected_step.get('Step No')} test failed. See details below.")

                with st.expander("Test Results", expanded=True):
                    st.code(result.stdout)
                    if result.stderr:
                        st.error("Errors:")
                        st.code(result.stderr)

                # Check for screenshots
                screenshots_dir = Path("screenshots")
                if screenshots_dir.exists():
                    screenshots = list(screenshots_dir.glob(f"*step{state.selected_step.get('Step No')}*.png"))
                    if screenshots:
                        st.subheader("Test Screenshots")
                        for screenshot in screenshots:
                            st.image(str(screenshot), caption=screenshot.name)

                # Set flags to indicate the current step is completed and ready for next step
                if not state.all_steps_done:
                    # Check if there are more steps
                    next_step_index = state.current_step_index + 1
                    if next_step_index < state.total_steps:
                        # Get the next step information
                        next_step = state.step_table_json[next_step_index]
                        next_step_no = next_step.get('step_no', 'N/A')

                        # Mark Stage 7 as complete
                        st.success(f"✅ Test Case Step {state.selected_step.get('Step No')} completed successfully!")
                        st.info("Automatically advancing to the next test case step...")

                        # Add a small delay to allow the user to see the success message
                        time.sleep(1.5)

                        # Store current step completion status
                        current_step_no = state.selected_step.get('Step No')
                        if not hasattr(state, 'completed_steps') or not isinstance(state.completed_steps, list):
                            state.completed_steps = []
                        if current_step_no not in state.completed_steps:
                            state.completed_steps.append(current_step_no)
                            logger.info(f"State change: added step {current_step_no} to completed_steps")

                        # The flag for script generation should already be set to True after test execution
                        # Double-check to make sure it's set
                        if not state.step_ready_for_script:
                            state.step_ready_for_script = True
                            logger.info("State change: step_ready_for_script = True (before automatic advancement)")

                        # Log the automatic advancement
                        logger.info(f"Automatically advancing to next step after test execution: {next_step_no}")

                        # Set up session state variables before calling advance_to_next_step
                        # since it will trigger a rerun if successful

                        # Set a message to be displayed in Stage 4
                        st.session_state['stage_progression_message'] = f"✅ Test Case Step {current_step_no} completed and automatically advanced to Step {next_step_no}"

                        # Add debug information to track the state update
                        st.session_state['auto_advance_debug'] = {
                            'timestamp': datetime.now().strftime("%H:%M:%S.%f"),
                            'from_step': current_step_no,
                            'to_step': next_step_no,
                            'current_step_index': state.current_step_index
                        }

                        # Set a flag to force a refresh after advancement
                        st.session_state['force_refresh_after_advance'] = {
                            'timestamp': datetime.now().strftime("%H:%M:%S.%f"),
                            'target_step': next_step_no,
                            'from_step': current_step_no
                        }

                        # Set a flag to indicate we're coming back from Stage 7
                        # This will be used in run_app() to stop at Stage 4
                        st.session_state['coming_from_stage7'] = True
                        logger.info(f"Setting coming_from_stage7 flag to return to Stage 4 with step {next_step_no}")

                        # Force state update in session state
                        st.session_state['state'] = state

                        # Add a more visible indicator that we're advancing
                        st.success("🔄 Advancing to the next step... Please wait.")

                        # Add a small delay to ensure the state is properly updated
                        time.sleep(0.5)

                        # Call advance_to_next_step to move to the next step
                        # This will call st.rerun() internally if successful
                        advance_to_next_step()

                        # The code below will only execute if advance_to_next_step() fails
                        # and doesn't trigger a rerun
                        st.error("Failed to automatically advance to the next step. Please check the logs.")
                        logger.error("Failed to automatically advance to the next step after test execution")
                    else:
                        # All steps processed
                        state.all_steps_done = True
                        st.success("✅ All test case steps have been processed!")

                        # Create a combined script file for the entire test case
                        combined_script_path = create_combined_script(state)
                        if combined_script_path:
                            st.success(f"📄 Created combined script file: {os.path.basename(combined_script_path)}")
                            with st.expander("View Combined Script", expanded=False):
                                with open(combined_script_path, 'r') as f:
                                    combined_script_content = f.read()
                                st.code(combined_script_content, language="python")
                                st.info(f"Combined script file: {combined_script_path}")
            except Exception as e:
                st.error(f"Error running test script: {e}")
                import traceback
                st.error(traceback.format_exc())
