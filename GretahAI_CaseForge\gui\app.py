"""
Main application entry point for the GUI.
"""

import streamlit as st
import pandas as pd
import json
import os
import io  # Add this import for BytesIO
from datetime import datetime, timedelta
import time
from collections import deque
from pathlib import Path
import altair as alt  # For visualizations
import sqlite3  # For direct database access
from openpyxl import Workbook
from openpyxl.styles import Alignment, Font
from openpyxl.utils import get_column_letter
import traceback

# Modify sys.path to allow Python to find the module
import sys
# Update the path to just point to the parent directory where Test_case_db_helper.py is located
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
# Also add the project root directory to help with imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

# Debug paths
print(f"Python paths: {sys.path}")
print(f"Current directory: {os.getcwd()}")

# Import database helper
import GretahAI_CaseForge.Test_case_db_helper as db  # Import the database helper
# Debug database path
print(f"Database path used by helper: {db.DATABASE_PATH}")
print(f"Database absolute path: {os.path.abspath(db.DATABASE_PATH)}")
print(f"Database path exists: {os.path.exists(os.path.abspath(db.DATABASE_PATH))}")

# Modify the database path if needed to point to the correct location
if not os.path.exists(os.path.abspath(db.DATABASE_PATH)):
    # Try to find the database in other common locations
    possible_paths = [
        os.path.join(os.getcwd(), "test_cases_v2.db"),
        os.path.join(os.path.dirname(__file__), "..", "test_cases_v2.db"),
        os.path.join(os.path.dirname(__file__), "..", "..", "test_cases_v2.db")
    ]

    for path in possible_paths:
        if os.path.exists(path):
            print(f"Found database at: {path}")
            # Set the database path in the helper module
            db.DATABASE_PATH = path
            break
    else:
        print("Could not find database file in common locations")

import importlib  # For reloading modules

# Fix the admin_config import by specifying the correct path
# First try all common locations for the admin_config.json file
possible_admin_config_paths = [
    os.path.join(os.path.dirname(__file__), "..", "admin_config.json"),  # Parent directory
    os.path.join(os.path.dirname(__file__), "..", "..", "admin_config.json"),  # Two levels up
    os.path.join(os.getcwd(), "admin_config.json"),  # Current working directory
    os.path.join(os.path.dirname(os.path.abspath(db.DATABASE_PATH)), "admin_config.json"),  # Same dir as DB
]

admin_config_path = None
for path in possible_admin_config_paths:
    if os.path.exists(path):
        admin_config_path = path
        print(f"Found admin config at: {path}")
        break

if admin_config_path is None:
    # Fallback to the original path as last resort
    admin_config_path = os.path.join(Path(__file__).parent.parent.parent, "admin_config.json")
    print(f"Using default admin config path: {admin_config_path}")

# Import utility functions
from GretahAI_CaseForge.gui.utils import (
    load_config,
    jira_connection,
    load_usage_data,
    save_usage_data,
    generate_test_scenarios,
    merge_excel_files
)

# Add a function to override the default config path
def get_config_path():
    """Return the absolute path to the config.json file."""
    # Try multiple possible locations for config.json
    possible_paths = [
        # First try: parent directory of GretahAI_CaseForge (project root)
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config.json"),
        # Second try: GretahAI_CaseForge directory
        os.path.join(os.path.dirname(os.path.dirname(__file__)), "config.json"),
        # Third try: current working directory
        os.path.join(os.getcwd(), "config.json"),
        # Fourth try: relative to gui directory
        os.path.join(os.path.dirname(__file__), "..", "..", "config.json"),
    ]

    for path in possible_paths:
        abs_path = os.path.abspath(path)
        if os.path.exists(abs_path):
            return abs_path

    # If not found, return the first path as default
    return os.path.abspath(possible_paths[0])

# Import Excel utility functions directly from helpers
from GretahAI_CaseForge.helpers import (
    create_formatted_excel_from_scenarios,
    parse_test_scenarios_json,
    get_latest_test_case_file
)

# Import page modules
from GretahAI_CaseForge.gui.sidebar import render_sidebar
from GretahAI_CaseForge.gui.test_generator import render_test_generator
from GretahAI_CaseForge.gui.test_analysis import render_test_analysis
from GretahAI_CaseForge.gui.visualization import render_visualization
from GretahAI_CaseForge.gui.reporting import render_reporting

# Replace the direct import with a function that loads the admin config
def load_admin_config():
    """Load admin configuration from JSON file."""
    try:
        # First attempt to use the found path
        if admin_config_path and os.path.exists(admin_config_path):
            with open(admin_config_path, 'r') as file:
                return json.load(file)

        # If that fails, search common locations again (redundant but safer)
        for path in possible_admin_config_paths:
            if os.path.exists(path):
                with open(path, 'r') as file:
                    return json.load(file)

        # If we get here, we couldn't find the file
        print("Warning: admin_config.json not found, using default values")
        return {"allow_delete_test_cases": False, "allow_clear_database": False, "users": []}
    except Exception as e:
        print(f"Failed to load admin configuration: {e}")
        return {"allow_delete_test_cases": False, "allow_clear_database": False, "users": []}

def run_app():
    """Main function to run the Streamlit application."""

    # Initialize session state for request timestamps
    if "google_request_timestamps" not in st.session_state:
        st.session_state.google_request_timestamps = []

    if "google_token_usage" not in st.session_state:
        st.session_state.google_token_usage = []

    # --- Page Setup ---
    st.set_page_config(
        page_title="GretahAI Case Generator",
        page_icon="📋",
        layout="wide",
        initial_sidebar_state="expanded",
    )

    # Load custom CSS file
    try:
        css_path = os.path.join(os.path.dirname(__file__), "style.css")
        print(f"Looking for CSS file at: {css_path}")
        if os.path.exists(css_path):
            with open(css_path) as f:
                st.markdown(f'<style>{f.read()}</style>', unsafe_allow_html=True)
                print("Successfully loaded custom CSS")
        else:
            print(f"CSS file not found at: {css_path}")
            # Fallback inline CSS
            st.markdown("""
            <style>
                .main-header {
                    font-size: 2.5rem;
                    color: #4A6FE3;
                    text-align: center;
                    margin-bottom: 1rem;
                }
                .sub-header {
                    font-size: 1.8rem;
                    color: #6C8EFF;
                    margin-top: 2rem;
                    margin-bottom: 1rem;
                }
                .success-box {
                    padding: 1rem;
                    border-radius: 0.5rem;
                    background-color: rgba(46, 204, 113, 0.1);
                    border-left: 5px solid #2ECC71;
                }
                .info-box {
                    padding: 1rem;
                    border-radius: 0.5rem;
                    background-color: rgba(52, 152, 219, 0.1);
                    border-left: 5px solid #3498DB;
                }
            </style>
            """, unsafe_allow_html=True)
    except Exception as e:
        print(f"Error loading CSS: {str(e)}")
        # Proceed without custom styling

    # --- Initialize Config and JIRA ---
    try:
        from utils import load_config
        config = load_config()  # Call without arguments

        if not config:
            st.error("🛑 Empty configuration loaded")
            config = {}  # Use empty dict as fallback
            jira_connected = False
            jira_client = None
        else:
            try:
                from utils import jira_connection
                jira_client = jira_connection(
                    config["jira_server"],
                    config["jira_user"],
                    config["jira_api_token"]
                )
                jira_connected = True
            except Exception as e:
                st.error(f"🛑 Failed to connect to JIRA: {e}")
                jira_connected = False
                jira_client = None

    except Exception as e:
        st.error(f"🛑 Failed to load configuration: {e}")
        config = {}  # Use empty dict as fallback
        jira_connected = False
        jira_client = None

    # --- Initialize remaining app components ---
    try:
        from gui.sidebar import render_sidebar
        from gui.test_generator import render_test_generator
        from gui.test_analysis import render_test_analysis

        # Initialize AI settings through sidebar
        ai_provider, google_api_key, selected_model = render_sidebar(jira_connected, config)

        # Render appropriate page based on selection
        if st.session_state.get("current_page") == "generator":
            render_test_generator(jira_client, ai_provider, google_api_key, selected_model, config)
        elif st.session_state.get("current_page") == "analysis":
            render_test_analysis()

    except Exception as e:
        st.error(f"An error occurred: {str(e)}")
        st.code(traceback.format_exc())

    # --- Footer ---
    st.markdown("---")

    # Footer Text with CaseForge branding
    st.markdown("""
    <div class="caseforge-footer">
        <strong>GretahAI CaseForge</strong> v1.1 | Powered by Cogniron | For support contact: <EMAIL>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    run_app()