# GretahAI CaseForge

[![Cogniron Logo](https://cogniron.com/wp-content/uploads/2024/10/image-69.png)](https://cogniron.com/)

**GretahAI CaseForge** is a Streamlit-based application for generating, managing, and organizing test cases with JIRA integration. It allows users to create test cases from JIRA issues, organize them by type, and export them to various formats.

## Features

* **JIRA Integration:** Connect directly to JIRA to fetch issues and create test cases.
* **AI-Powered Test Case Generation:** Generate comprehensive test cases from JIRA issues using AI.
* **Test Case Management:** Organize test cases by type (positive, negative, security, performance, mixed).
* **Database Storage:** Store and manage test cases in a SQLite database.
* **Excel Import/Export:** Import test cases from Excel files and export them to Excel.
* **CSV Export:** Export test cases to CSV format for use with other tools.
* **User Management:** Track which users created or modified test cases.
* **Test Run Tracking:** Create and manage test runs to track test case execution.

## Installation

### Prerequisites

* Python 3.9+
* Streamlit
* Pandas
* JIRA access

### Steps

1. **Navigate to the Application Directory:**
   ```bash
   cd GretahAI_CaseForge
   ```

2. **Install Dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure JIRA Access:**
   Create a `config.json` file in the repository root with your JIRA credentials:
   ```json
   {
     "jira_server": "YOUR_JIRA_SERVER_URL",
     "jira_user": "YOUR_JIRA_USERNAME",
     "jira_api_token": "YOUR_JIRA_API_TOKEN"
   }
   ```

4. **Initialize the Database:**
   ```bash
   python Test_case_db_helper.py
   ```

## Usage

1. **Start the Application:**
   ```bash
   streamlit run gui.py
   ```

2. **Navigate the Interface:**
   * Use the sidebar to switch between "Test Generator" and "Test Analysis" sections.
   * The Test Generator section allows you to create new test cases.
   * The Test Analysis section allows you to view and analyze existing test cases.

3. **Generate Test Cases:**
   * Enter a JIRA issue ID (e.g., "TP-10").
   * Select the test type (positive, negative, security, performance, mixed).
   * Choose the AI provider and model.
   * Click "Generate Test Cases" to create test cases based on the JIRA issue.

4. **Import/Export Test Cases:**
   * Upload an Excel file to import test cases.
   * Click "Update Database" to add the imported test cases to the database.
   * Use the export options to export test cases to Excel or CSV.

## Directory Structure

```
GretahAI_CaseForge/
├── gui/                  # UI components
│   ├── Test_cases/       # Stored test cases
│   ├── attached_images/  # Images attached to test cases
│   ├── raw_responses/    # Raw AI responses
│   ├── edited_excel/     # Edited Excel files
│   └── csv_exports/      # Exported CSV files
├── db/                   # Database utilities
├── utils/                # Utility functions
├── gui.py                # Main Streamlit application
├── Test_case_db_helper.py # Database helper functions
├── helpers.py            # Helper functions
└── README.md             # This file
```

## Database Schema

The application uses a SQLite database with the following main tables:

* **test_runs:** Stores information about test runs, including the JIRA ID, test type, user, and notes.
* **test_cases:** Stores individual test cases, linked to test runs.
* **test_case_executions:** Tracks the execution of test cases.

Database schema management is handled by the `Test_case_db_helper.py` module, which provides comprehensive functionality for schema standardization, verification, and migration.

## AI Integration

The application uses AI to generate test cases from JIRA issues. It supports:

* **Google AI Studio (Gemini):** Uses the Gemini API to generate test cases.
* **Other AI providers:** The application is designed to be extensible to support other AI providers.

## Workflow

1. **Fetch JIRA Issue:** The application fetches the JIRA issue details, including the summary and description.
2. **Generate Test Cases:** The AI generates test cases based on the JIRA issue details.
3. **Store Test Cases:** The generated test cases are stored in the database.
4. **Export Test Cases:** The test cases can be exported to Excel or CSV for use with other tools.

## Integration with Other Applications

GretahAI CaseForge integrates with:

* **GretahAI ScriptWeaver:** Export test cases to be used for generating test scripts.
* **GretahAI TestInsight:** Export test cases to be used for test execution and analysis.

## Troubleshooting

* **JIRA Connection Issues:** Ensure your JIRA credentials are correct in the `config.json` file.
* **Database Issues:** Run `python Test_case_db_helper.py` to initialize or update the database.
* **Excel Import Issues:** Ensure your Excel file follows the expected format.

## Contributing

[Contribution guidelines]

## License

[License information]

## Contact

[Contact information]
