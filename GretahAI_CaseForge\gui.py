#!/usr/bin/env python3

import streamlit as st
import pandas as pd
import json
from jira import JIRA
from datetime import datetime, timedelta # Added timedelta
import time # Added time for potential refresh logic if needed
from collections import deque # Added deque for efficient time-window tracking
import os # Import os module
from pathlib import Path # Import Path for better path handling
import altair as alt # For visualizations
import io # For I/O operations
import sys # For system operations
import base64 # For encoding images
import re # For regular expressions
import sqlite3 # For direct database access
from contextlib import redirect_stdout # For capturing console output
import Test_case_db_helper as db # Import database helper

from openpyxl import Workbook
from openpyxl.styles import Alignment, Font
from openpyxl.utils import get_column_letter

# Import helper functions
from helpers import (
    extract_test_info_from_issue,
    run_ollama_with_chat,
    create_formatted_excel_from_scenarios,
    is_ollama_running,
    generate_gemini_test_gen_prompt,
    run_google_ai_studio, # Ensure this function returns (response_text, token_count)
    upload_edited_excel,
    get_latest_test_case_file,
    parse_test_scenarios_json
)

# --- Caching and Session State Setup ---

@st.cache_resource
def load_config():
    with open("config.json", "r") as f:
        config = json.load(f)
        return config

@st.cache_resource
def jira_connection(server, user, token):
    options = {"server": server}
    return JIRA(options=options, basic_auth=(user, token))

def generate_test_scenarios(case_id, test_type, num_scenarios, selected_model, jira_client, ai_provider, google_api_key, is_all_test_types=False, continue_numbering=False, test_run_id=None):
    # Database helper is already imported at the top of the file

    # Create Test_cases folder if it doesn't exist (for raw responses)
    test_cases_dir = Path("Test_cases")
    test_cases_dir.mkdir(exist_ok=True)

    # Initialize database if it doesn't exist
    db.init_db(db.DATABASE_PATH)

    # Check if test cases exist in the database for this JIRA ID and test type
    # If this is part of the "All Test Case Types" option, use "all"
    # Otherwise, use the test_type parameter
    dashboard_test_type = "all" if is_all_test_types else test_type
    highest_id = db.get_highest_test_case_id_number(db.DATABASE_PATH, case_id, dashboard_test_type)
    test_cases_exist = highest_id > 0

    # Track if we're creating new test cases
    creating_new_test_cases = not test_cases_exist

    # Reset the counter if no test cases exist for this JIRA ID and test type
    # But only if we're not continuing numbering from a previous test type
    if not test_cases_exist and not continue_numbering:
        # For specific test types (not 'all'), always reset the counter if no test cases exist
        # This ensures that each specific test type starts from TC_001 when generated for the first time
        if test_type != 'all' or not test_cases_exist:
            from helpers import reset_test_case_counter, set_test_case_counter
            reset_test_case_counter()
            set_test_case_counter(0)  # Explicitly set to 0 to ensure it starts from TC_001
            print(f"Test case counter reset to 0 for {case_id} ({test_type}). Creating new test cases.")

    # Store the test case status in session state for this test type
    if f"test_case_status_{case_id}_{test_type}" not in st.session_state:
        st.session_state[f"test_case_status_{case_id}_{test_type}"] = {}

    st.session_state[f"test_case_status_{case_id}_{test_type}"] = {
        "exists": test_cases_exist,
        "creating_new": creating_new_test_cases,
        "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S")
    }

    # Fetch issue from JIRA
    issue = jira_client.issue(case_id)

    # Update the JIRA issue in the database with the actual summary and description from the JIRA API
    summary = issue.fields.summary
    description = issue.fields.description or ""
    status = issue.fields.status.name
    db.update_jira_issue_in_database(db.DATABASE_PATH, case_id, summary, description, status)

    # Check for attachments in the JIRA issue
    attachment_path = None
    if hasattr(issue.fields, 'attachment') and issue.fields.attachment:
        # Create a directory for storing attachments if it doesn't exist
        attached_images_dir = Path("attached_images")
        attached_images_dir.mkdir(exist_ok=True)

        # Map filename → attachment URL
        # The 'content' attribute contains the URL to download the attachment
        att_map = {}
        for att in issue.fields.attachment:
            try:
                att_map[att.filename] = att.get_data()
            except Exception as e:
                print(f"Error getting attachment data for {att.filename}: {e}")
                # Try to get the content URL directly if available
                if hasattr(att, 'content'):
                    att_map[att.filename] = att.content

        # Download each attachment to the attached_images folder
        local_map = {}
        for fname, data_or_url in att_map.items():
            # Only download image files
            if any(fname.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']):
                try:
                    unique_name = f"{case_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{fname}"
                    file_path = attached_images_dir / unique_name

                    # Check if we have binary data or a URL
                    if isinstance(data_or_url, bytes):
                        # We already have the binary data
                        with open(file_path, "wb") as f:
                            f.write(data_or_url)
                        local_map[fname] = str(file_path)
                        print(f"Saved attachment data: {fname} to {file_path}")
                    elif isinstance(data_or_url, str) and data_or_url.startswith('http'):
                        # We have a URL, need to download
                        resp = jira_client._session.get(data_or_url, stream=True)
                        if resp.status_code == 200:
                            with open(file_path, "wb") as f:
                                f.write(resp.content)
                            local_map[fname] = str(file_path)
                            print(f"Downloaded attachment: {fname} to {file_path}")
                        else:
                            print(f"⚠️ Failed to download {fname}: HTTP {resp.status_code}")
                    else:
                        print(f"⚠️ Unsupported attachment data type for {fname}: {type(data_or_url)}")
                except Exception as e:
                    print(f"⚠️ Error processing attachment {fname}: {e}")

        # Use the first image attachment for visual analysis
        attachment_path = next((path for path in local_map.values() if os.path.exists(path)), None)
        if attachment_path:
            print(f"Using attachment for visual analysis: {attachment_path}")    # Get the prompt based on the test type
    # Only use enhanced description if not generating all test types
    use_enhanced = st.session_state.get("use_enhanced_description", True) and not kwargs.get("is_all_test_types", False)
    enhanced_desc = st.session_state.get("enhanced_description") if use_enhanced else None

    # Only use enhanced description if it's not empty/whitespace
    if enhanced_desc and not enhanced_desc.strip():
        enhanced_desc = None

    # Generate prompt based on AI provider
    if ai_provider == "Local":
        combined_text = extract_test_info_from_issue(issue, test_type=test_type, num_scenarios=num_scenarios, enhanced_description=enhanced_desc if not kwargs.get("is_all_test_types", False) else None)
    else:  # Google AI Studio
        combined_text = generate_gemini_test_gen_prompt(issue, test_type=test_type, num_scenarios=num_scenarios, attachment_path=attachment_path, enhanced_description=enhanced_desc if not kwargs.get("is_all_test_types", False) else None)

    response_text = ""
    token_count = 0  # Initialize token count

    # Generate timestamp for the raw response filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")    # Generate test Cases using the selected AI provider
    start_time = datetime.now()
    # Add a slider in the sidebar to configure the temperature
    st.sidebar.slider(
        "Set Temperature for AI Model",
        min_value=0.0,
        max_value=1.0,
        value=st.session_state.get("format_temperature", 0.7),
        step=0.1,
        key="format_temperature"
    )
    # Get temperature from session state
    temperature = st.session_state["format_temperature"]

    if ai_provider == "Local":
        response_text = run_ollama_with_chat(combined_text, model=selected_model, temperature=temperature)
        # Note: Local Ollama token tracking is not implemented here
        token_count = None  # Indicate token count is not available for local

    elif ai_provider == "Google AI Studio":
        if not google_api_key:
            raise ValueError("Google AI Studio API Key is required.")        # Handle the case where combined_text is a tuple (image, prompt) from generate_gemini_test_gen_prompt
        # or a regular string prompt - never use temperature for test generation
        result = run_google_ai_studio(combined_text, api_key=google_api_key, model=selected_model)

        # Check if the result is a tuple (text, token_count) or just an error message
        if isinstance(result, tuple) and len(result) == 2:
            response_text, token_count = result
        else:
            # If it's just a string (error message)
            response_text = str(result)  # Ensure it's a string
            token_count = None

        # If we used an attachment, log that information
        if attachment_path and os.path.exists(attachment_path):
            print(f"Used attachment in prompt: {attachment_path}")
            # Check if the response already has the visual analysis note
            if not response_text.startswith("Generated with visual analysis of attachment:"):
                # Add a note to the response text about the attachment being used
                response_text = f"Generated with visual analysis of attachment: {os.path.basename(attachment_path)}\n\n{response_text}"

        # --- Record Google AI Usage ---
        current_time = datetime.now()
        st.session_state.google_request_timestamps.append(current_time)
        if token_count is not None and token_count > 0:
            st.session_state.google_token_usage.append((current_time, token_count))
        save_usage_data()
        # -----------------------------

    else:
        raise ValueError("Invalid AI provider selected.")

    processing_time = (datetime.now() - start_time).total_seconds()

    # Save the raw response before trying to parse it
    raw_response_file = test_cases_dir / f"Latest_testcases_{case_id}_{test_type}_{timestamp}_raw.txt"
    try:
        with open(raw_response_file, "w", encoding="utf-8") as f:
            f.write(response_text)
    except Exception as e:
        st.warning(f"Failed to save raw response: {e}")

    # Parse the response and save to database (only if response_text is generated)
    if response_text:
        try:
            # Set the dashboard_test_type based on what was selected in the dropdown
            # If this is part of the "All Test Case Types" option, use "all"
            # Otherwise, use the test_type parameter
            dashboard_test_type = "all" if is_all_test_types else test_type

            # Parse the response text into a DataFrame
            # Pass the dashboard_test_type to the function
            df = parse_test_scenarios_json(response_text, issue, start_id_from=None, continue_numbering=continue_numbering, dashboard_test_type=dashboard_test_type)

            # Get the current user from session state (if logged in)
            current_user = st.session_state.get("admin_username", "anonymous")

            # Create a test run if one wasn't provided
            if test_run_id is None:
                test_run_id = db.create_test_run(
                    db.DATABASE_PATH,
                    case_id,
                    dashboard_test_type,
                    user_name=current_user,
                    notes=f"Generated with {ai_provider} using {selected_model}"
                )

            # Save the DataFrame to the database with user info and test run ID
            db_success = db.save_test_cases_to_database(
                db.DATABASE_PATH,
                case_id,
                df,
                dashboard_test_type,
                user_name=current_user,
                test_run_id=test_run_id
            )

            if db_success:
                print(f"Test cases saved to database successfully for {case_id} ({test_type}).")
                # Create a virtual output file path for compatibility with existing code
                output_file = f"database://{case_id}/{dashboard_test_type}/{timestamp}"

                # Force update the session state to ensure the tabs are displayed
                st.session_state.scenario_data = {
                    "issue": issue,
                    "response": response_text,
                    "output_file": output_file,
                    "processing_time": processing_time,
                    "ai_provider": ai_provider,
                    "model_used": selected_model,
                    "tokens_used": token_count,
                    "test_type": test_type,
                    "start_id": None,  # Will be calculated later
                    "end_id": None,  # Will be calculated later,
                    "used_attachment": attachment_path is not None and os.path.exists(attachment_path),
                    "attachment_name": os.path.basename(attachment_path) if attachment_path and os.path.exists(attachment_path) else None,
                    "num_test_cases": len(df["Test Case ID"].dropna().unique()) if "Test Case ID" in df.columns else 0,
                    "has_issues": False,
                    "issues": []
                }
            else:
                st.error("Failed to save test cases to database.")
                output_file = None
        except Exception as e:
            error_message = f"Failed to parse and save test cases: {str(e)}"
            st.error(error_message)
            st.error("Raw response may not be in valid JSON format. Please try generating test cases again.")

            # If we have a test run ID, update it with the error information
            if test_run_id is not None:
                db.update_test_run(
                    db.DATABASE_PATH,
                    test_run_id,
                    num_test_cases=0,
                    status="failed",
                    notes=f"Error: {error_message}"
                )

            # Return a tuple that includes the raw response for debugging
            return issue, response_text, None, processing_time, token_count
    else:
        st.warning("AI did not return a valid response. Test cases not generated.")
        output_file = None # No output file if no response

    return issue, response_text, output_file, processing_time, token_count # Return token_count

# --- Initialize Session State ---
# Store generated data
if "scenario_data" not in st.session_state:
    st.session_state.scenario_data = None

# Load usage data from file
def load_usage_data():
    try:
        with open("usage_data.json", "r") as f:
            data = json.load(f)
            # Convert timestamps back to datetime objects
            request_timestamps = [datetime.fromisoformat(ts) for ts in data.get("request_timestamps", [])]
            token_usage = [(datetime.fromisoformat(ts), count) for ts, count in data.get("token_usage", [])]
            return request_timestamps, token_usage
    except FileNotFoundError:
        return [], []
    except Exception as e:
        st.error(f"Error loading usage data: {e}")
        return [], []

# Save usage data to file
def save_usage_data():
    try:
        # Convert datetime objects to ISO format strings for JSON serialization
        request_timestamps = [ts.isoformat() for ts in st.session_state.google_request_timestamps]
        token_usage = [(ts.isoformat(), count) for ts, count in st.session_state.google_token_usage]

        data = {
            "request_timestamps": request_timestamps,
            "token_usage": token_usage
        }
        with open("usage_data.json", "w") as f:
            json.dump(data, f)
    except Exception as e:
        st.error(f"Error saving usage data: {e}")

# Initialize session states
if "scenario_data" not in st.session_state:
    st.session_state.scenario_data = None

# Load Google AI usage data from file
initial_request_timestamps, initial_token_usage = load_usage_data()

if "google_request_timestamps" not in st.session_state:
    st.session_state.google_request_timestamps = initial_request_timestamps
if "google_token_usage" not in st.session_state:
    st.session_state.google_token_usage = initial_token_usage

# Store Google AI usage data (using deque for potential efficiency with time windows)
# Limit deque size? For RPM/TPM maybe, but RPD needs 24h. Lists might be simpler unless memory is a concern.
# Let's stick to lists for simplicity now, can optimize later if needed.
if "google_request_timestamps" not in st.session_state:
    st.session_state.google_request_timestamps = [] # Store datetime objects
if "google_token_usage" not in st.session_state:
    st.session_state.google_token_usage = [] # Store tuples of (datetime, token_count)

# --- Page Setup ---

st.set_page_config(
    page_title="GretahAI Case Generator",
    page_icon="📋",
    layout="wide",
    initial_sidebar_state="expanded",
)

# Custom CSS (keep as is)
st.markdown("""
<style>
    /* CSS remains the same */
    .main-header {
        font-size: 2.5rem;
        color: #1E88E5;
        text-align: center;
        margin-bottom: 1rem;
    }
    .sub-header {
        font-size: 1.8rem;
        color: #26A69A;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .success-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #E8F5E9;
        border-left: 5px solid #4CAF50;
    }
    .info-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #E3F2FD;
        border-left: 5px solid #2196F3;
    }
    .stButton > button {
        width: 100%;
        border-radius: 0.5rem;
        background-color: #1E88E5;
        color: white;
        font-weight: bold;
        padding: 0.5rem 1rem;
        border: none;
    }
    .stDownloadButton > button {
        width: 100%;
        border-radius: 0.5rem;
        background-color: #4CAF50;
        color: white;
        font-weight: bold;
        padding: 0.5rem 1rem;
        border: none;
    }
    .stTextArea textarea {
        border-radius: 0.5rem;
        border: 1px solid #BDBDBD;
    }
    div[data-testid="stDataFrame"] {
        width: 100%;
    }
</style>
""", unsafe_allow_html=True)

# --- Load Config and Connect ---
try:
    config = load_config()
    jira_client = jira_connection(config["jira_server"], config["jira_user"], config["jira_api_token"])
    jira_connected = True
except FileNotFoundError:
    st.error("🛑 config.json not found. Please create the configuration file.")
    st.stop()
except Exception as e:
    st.error(f"🛑 Failed to load config or connect to JIRA: {e}")
    jira_connected = False
    # Allow app to continue to show sidebar/UI but disable JIRA-dependent features
    # st.stop() # Or stop execution completely

# Sidebar configuration
with st.sidebar:
    st.image("https://cogniron.com/wp-content/uploads/2024/10/image-69.png", width=300)

    # Initialize the page state if it doesn't exist
    if "current_page" not in st.session_state:
        st.session_state["current_page"] = "generator"

    # Navigation section at the top
    st.markdown("### Navigation")

    # Create radio buttons for navigation that look like the other inputs
    current_page = st.radio(
        "Select Section",
        options=["Test Generator", "Test Analysis"],
        index=0 if st.session_state["current_page"] == "generator" else 1,
        key="navigation_radio",
        horizontal=True
    )

    # Update the session state based on selection
    if current_page == "Test Generator" and st.session_state["current_page"] != "generator":
        st.session_state["current_page"] = "generator"
        st.rerun()
    elif current_page == "Test Analysis" and st.session_state["current_page"] != "analysis":
        st.session_state["current_page"] = "analysis"
        st.rerun()

    # Initialize variables that need to be available globally
    uploaded_file = None
    ai_provider = "Local"  # Default value
    selected_model = "gemma:1b"  # Default value
    google_api_key = ""  # Default value

    # Only show AI settings if we're on the Test Generator page
    if st.session_state["current_page"] == "generator":
        st.markdown("---")
        # Add AI Provider selection
        ai_provider = st.radio(
            "Select AI Provider",
            options=["Local", "Google AI Studio"],
            index=0, # Default to Local
            key="ai_provider_radio" # Add a key for stability
        )

        # Get Google API key from config but don't show the input field
        try:
            google_api_key = config.get("google_api_key", "")
        except:
            pass

        st.markdown("### Model Settings")
        # Adjust model options based on provider (example)
        if ai_provider == "Local":
            # Example local models - adjust based on what 'ollama list' shows
            model_options = ["gemma:1b", "gemma:4b", "mistral"] # Replace with actual available local models
            provider_tag = "📍 **LOCAL**"
            # Try to get actual local models if Ollama is running
            try:
                ollama_running, ollama_version = is_ollama_running()
                if not ollama_running:
                    st.warning("⚠️ Ollama not detected. Local AI provider may not work.")
            except Exception:
                pass
        else: # Google AI Studio
            model_options = ["gemini-1.5-flash", "gemini-1.5-flash-8b"]
            provider_tag = "☁️ **GOOGLE**"

        col1, col2 = st.columns([3, 1])
        with col1:
            # Use a different key for the selectbox depending on the provider to avoid state issues
            model_key = f"model_select_{ai_provider.lower().replace(' ', '_')}"
            selected_model = st.selectbox("Select AI Model", model_options, key=model_key)
        with col2:
            st.markdown(provider_tag)

        st.markdown("---")
        st.markdown("### Upload Edited Test Cases")
        uploaded_file = st.file_uploader("Upload edited Excel file", type=["xlsx"], help="Upload an edited test case Excel file to store it in the system")
    else:
        # If we're on the Test Analysis page, show login status and options
        st.markdown("---")

        # Check if the user is logged in
        is_admin_logged_in = st.session_state.get("is_admin_logged_in", False)
        current_user = st.session_state.get("admin_username", "")

        if is_admin_logged_in and current_user:
            st.success(f"Logged in as: {current_user}")

            # Add logout button
            if st.button("Logout", key="sidebar_analysis_logout"):
                st.session_state["admin_username"] = ""
                st.session_state["is_admin_logged_in"] = False
                st.session_state["admin_user"] = ""
                st.rerun()
        else:
            st.info("Please log in through the Admin Panel to view your test runs.")

    # Only show upload options if we're on the Generator page and have an uploaded file
    if st.session_state["current_page"] == "generator" and "uploaded_file" in locals() and uploaded_file is not None:
        # Add information about the upload options
        with st.expander("About Upload Options"):
            st.markdown("### Update Database")
            st.info("This option will save the edited test cases to the database with the 'is_edited' flag set to True. When viewing test cases, edited versions will be prioritized over original versions.")

            st.markdown("### Jira Integration (Coming Soon)")
            st.info("When implemented, this option will:")
            st.markdown("""1. Convert the Excel file to CSV format
            2. Upload the CSV to your Jira project using credentials from config.json
            3. Link the test cases to the User Story IDs specified in the Excel file""")

        # Store the uploaded file in session state for later use
        st.session_state["uploaded_excel_file"] = uploaded_file
        st.success(f"Excel file '{uploaded_file.name}' uploaded successfully. Choose an action below:")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("Update Database"):
                with st.spinner("Updating database..."):
                    # Get the current logged-in user
                    current_user = st.session_state.get("admin_username", "admin")
                    success, message, file_path = upload_edited_excel(st.session_state["uploaded_excel_file"], current_user)
                    if success:
                        st.success(message)
                    else:
                        st.error(message)

        with col2:
            # Jira Integration button (disabled for now)
            if st.button("Jira Integration", disabled=False, help="Upload test cases to Jira Zephyr Scale"):
                if "uploaded_excel_file" in st.session_state:
                    with st.spinner("Uploading test cases to Jira Zephyr Scale..."):
                        try:
                            # Read the Excel file into a DataFrame
                            df = pd.read_excel(st.session_state["uploaded_excel_file"])

                            # Extract user story IDs from the DataFrame
                            user_story_ids = df["User Story ID"].dropna().unique().tolist()

                            # Get Jira credentials from config
                            jira_url = config["jira_server"]
                            jira_username = config["jira_user"]
                            jira_api_token = config["jira_api_token"]

                            # Import the Zephyr integration module
                            from helpers import load_zephyr_integration
                            if load_zephyr_integration():
                                # This will import the upload_test_cases_to_jira function
                                from zephyr_integration import upload_test_cases_to_jira

                                # Upload test cases to Jira
                                success, message = upload_test_cases_to_jira(
                                    dataframe=df,
                                    jira_url=jira_url,
                                    jira_username=jira_username,
                                    jira_password=jira_api_token,
                                    user_story_ids=user_story_ids
                                )

                                if success:
                                    st.success(message)
                                else:
                                    st.error(message)
                            else:
                                st.error("Zephyr integration module not available")
                        except Exception as e:
                            st.error(f"Error uploading test cases to Jira: {str(e)}")
                else:
                    st.warning("Please upload an Excel file first")
            # Keep the info message for now
            st.info("Jira integration with Zephyr Scale is now available.")

    st.markdown("---")

    # Add Admin Panel
    # Check if we should automatically open the Admin Panel
    auto_open_admin = st.session_state.get("open_admin_panel", False)
    if auto_open_admin:
        # Reset the flag
        st.session_state["open_admin_panel"] = False

    # Import admin configuration and database helper
    import admin_config
    import Test_case_db_helper as db
    # Reload the module to ensure we have the latest version with our new function
    import importlib
    importlib.reload(db)

    with st.expander("🔐 Admin Panel", expanded=auto_open_admin):
        st.warning("⚠️ This panel provides administrative functions that can delete data.")

        # Create tabs for different admin functions
        admin_tabs = st.tabs(["Login", "User Management", "Settings", "Database Operations"])

        # Login Tab
        with admin_tabs[0]:
            st.subheader("Admin Login")

            # Username and password inputs
            admin_username = st.text_input("Username", key="admin_username_input")
            admin_password = st.text_input("Password", type="password", key="admin_password_input")

            # Login button with better styling
            col1, col2 = st.columns([1, 3])
            with col1:
                login_button = st.button("🔐 Login", key="admin_login_button")

            # Handle login button
            if login_button:
                if admin_username and admin_password:
                    with st.spinner("Logging in..."):
                        if admin_config.verify_user(admin_username, admin_password):
                            if admin_config.is_admin_user(admin_username):
                                st.session_state["admin_logged_in"] = True
                                st.session_state["admin_username"] = admin_username
                                # Set is_admin_logged_in flag to share with test runs tab
                                st.session_state["is_admin_logged_in"] = True
                                # Store the admin user in a separate variable for the test runs tab
                                st.session_state["admin_user"] = admin_username
                                st.success(f"Logged in as {admin_username}")
                                st.rerun()
                            else:
                                st.error("You do not have admin privileges")
                        else:
                            st.error("Invalid username or password")
                else:
                    st.error("Username and password are required")

            # Check if a user is logged in
            is_logged_in = st.session_state.get("admin_logged_in", False)
            logged_in_username = st.session_state.get("admin_username", "")

            if is_logged_in:
                st.success(f"Logged in as {logged_in_username}")

                # Logout button with better styling
                col1, col2 = st.columns([1, 3])
                with col1:
                    logout_button = st.button("🚪 Logout", key="admin_logout_button")

                # Handle logout button
                if logout_button:
                    with st.spinner("Logging out..."):
                        st.session_state["admin_logged_in"] = False
                        st.session_state["admin_username"] = ""
                        # Clear is_admin_logged_in flag
                        st.session_state["is_admin_logged_in"] = False
                        # Clear admin_user
                        st.session_state["admin_user"] = ""
                        st.rerun()

        # User Management Tab
        with admin_tabs[1]:
            st.subheader("User Management")

            # Check if admin is logged in
            is_logged_in = st.session_state.get("admin_logged_in", False)
            logged_in_username = st.session_state.get("admin_username", "")

            if not is_logged_in:
                st.warning("Please login as an admin to manage users")
            else:
                # Display existing users
                users = admin_config.get_config("users")
                if users:
                    st.write("Existing Users:")
                    user_data = []
                    for user in users:
                        user_data.append({
                            "Username": user.get("username"),
                            "Role": user.get("role"),
                            "Created": user.get("created_at", "").split("T")[0] if "T" in user.get("created_at", "") else user.get("created_at", "")
                        })
                    st.dataframe(user_data)

                # Add new user section
                st.write("---")
                st.subheader("Add New User")
                new_username = st.text_input("New Username", key="new_username_input")
                new_password = st.text_input("New Password", type="password", key="new_password_input")
                new_role = st.selectbox("Role", options=["user", "admin"], key="new_role_select")

                # Use columns for better button layout
                col1, col2 = st.columns([1, 3])
                with col1:
                    add_user_button = st.button("➕ Add User", key="add_user_button")

                # Handle add user button
                if add_user_button:
                    if new_username and new_password:
                        with st.spinner("Adding user..."):
                            success = admin_config.add_user(
                                new_username,
                                new_password,
                                new_role,
                                logged_in_username,
                                admin_password
                            )
                            if success:
                                st.success(f"User '{new_username}' added successfully")
                                st.rerun()
                            else:
                                st.error("Failed to add user. Make sure you have admin privileges and the username is unique.")
                    else:
                        st.error("Username and password are required")

                # Delete user section
                st.write("---")
                st.subheader("Delete User")

                # Get usernames for selection
                usernames = [user.get("username") for user in users if user.get("username") != logged_in_username]
                if usernames:
                    delete_username = st.selectbox("Select User to Delete", options=usernames, key="delete_username_select")

                    # Use columns for better button layout
                    col1, col2 = st.columns([1, 3])
                    with col1:
                        delete_user_button = st.button("🗑️ Delete User", key="delete_user_button")

                    # Add confirmation checkbox
                    with col2:
                        if delete_user_button:
                            confirm_delete = st.checkbox("Confirm deletion", key="delete_user_confirm")

                    # Handle delete user button
                    if delete_user_button:
                        if delete_username:
                            if 'confirm_delete' in locals() and confirm_delete:
                                with st.spinner("Deleting user..."):
                                    success = admin_config.delete_user(
                                        delete_username,
                                        logged_in_username,
                                        admin_password
                                    )
                                    if success:
                                        st.success(f"User '{delete_username}' deleted successfully")
                                        st.rerun()
                                    else:
                                        st.error("Failed to delete user. Make sure you have admin privileges.")
                            else:
                                st.error("Please confirm deletion by checking the box")
                else:
                    st.info("No other users to delete")

                # Change own password section
                st.write("---")
                st.subheader("Change Your Password")
                current_password = st.text_input("Current Password", type="password", key="current_password_input")
                new_password = st.text_input("New Password", type="password", key="change_new_password_input")
                confirm_password = st.text_input("Confirm New Password", type="password", key="confirm_new_password_input")

                # Use columns for better button layout
                col1, col2 = st.columns([1, 3])
                with col1:
                    change_pwd_button = st.button("🔄 Change Password", key="change_own_password_button")

                # Handle change password button
                if change_pwd_button:
                    if current_password and new_password and confirm_password:
                        if new_password == confirm_password:
                            with st.spinner("Changing password..."):
                                success = admin_config.change_user_password(
                                    logged_in_username,
                                    current_password,
                                    new_password
                                )
                                if success:
                                    st.success("Password changed successfully")
                                else:
                                    st.error("Failed to change password. Current password may be incorrect.")
                        else:
                            st.error("New passwords do not match")
                    else:
                        st.error("All fields are required")

        # Settings Tab
        with admin_tabs[2]:
            st.subheader("Settings")

            # Check if admin is logged in
            is_logged_in = st.session_state.get("admin_logged_in", False)
            logged_in_username = st.session_state.get("admin_username", "")

            if not is_logged_in:
                st.warning("Please login as an admin to change settings")
            else:
                # Get current settings
                allow_delete = admin_config.get_config("allow_delete_test_cases")
                allow_clear = admin_config.get_config("allow_clear_database")

                # Display current settings
                st.info(f"Delete test cases without password: {'Allowed' if allow_delete else 'Requires Admin Password'}")
                st.info(f"Clear entire database without password: {'Allowed' if allow_clear else 'Requires Admin Password'}")

                # Custom CSS for smaller buttons
                st.markdown("""
                <style>
                .admin-small-button {
                    font-size: 0.8rem !important;
                    padding: 0.3rem 0.5rem !important;
                    height: auto !important;
                }
                </style>
                """, unsafe_allow_html=True)

                col1, col2 = st.columns(2)

                with col1:
                    if st.button("Allow Delete Test Cases", key="allow_delete_button", help="Allow deleting test cases without admin password"):
                        admin_config.update_config("allow_delete_test_cases", True)
                        st.success("Setting updated successfully")
                        st.rerun()

                    if st.button("Allow Clear Database", key="allow_clear_button", help="Allow clearing the entire database without admin password"):
                        admin_config.update_config("allow_clear_database", True)
                        st.success("Setting updated successfully")
                        st.rerun()

                with col2:
                    if st.button("Require Password for Delete", key="require_password_delete_button", help="Require admin password to delete test cases"):
                        admin_config.update_config("allow_delete_test_cases", False)
                        st.success("Setting updated successfully")
                        st.rerun()

                    if st.button("Require Password for Clear", key="require_password_clear_button", help="Require admin password to clear the database"):
                        admin_config.update_config("allow_clear_database", False)
                        st.success("Setting updated successfully")
                        st.rerun()

        # Database Operations Tab
        with admin_tabs[3]:
            st.subheader("Database Operations")

            # Check if admin is logged in
            is_logged_in = st.session_state.get("admin_logged_in", False)
            logged_in_username = st.session_state.get("admin_username", "")

            if not is_logged_in:
                st.warning("Please login as an admin to perform database operations")
            else:
                # Create tabs for different deletion operations
                delete_tabs = st.tabs(["Delete by JIRA ID", "Delete Edited Test Cases", "Delete by Time Range"])

                # Tab 1: Delete by JIRA ID
                with delete_tabs[0]:
                    # JIRA ID and test type for deletion
                    delete_jira_id = st.text_input("JIRA ID", key="delete_jira_id_input")
                    delete_test_type = st.selectbox(
                        "Test Type to Delete",
                        options=["all", "positive", "negative", "security", "performance"],
                        key="delete_test_type_select"
                    )

                    # Add some spacing
                    st.write("")

                    # Delete button
                    if st.button("🗑️ Delete Test Cases",
                                key="delete_test_cases_button",
                                help="Delete test cases for the specified JIRA ID and test type"):
                        if delete_jira_id:
                            with st.spinner("Deleting test cases..."):
                                success = db.clear_database_for_jira(db.DATABASE_PATH, delete_jira_id, delete_test_type, admin_password)
                                if success:
                                    st.success(f"Successfully deleted test cases for {delete_jira_id} ({delete_test_type})")
                                else:
                                    st.error("Failed to delete test cases. Admin password may be required.")
                        else:
                            st.error("Please enter a JIRA ID")

                # Tab 2: Delete Edited Test Cases
                with delete_tabs[1]:
                    st.info("This will delete test cases that are marked as edited (is_edited = 1). These are typically test cases that have been uploaded through the 'Upload Edited Excel' feature.")

                    # JIRA ID for deletion (optional)
                    delete_edited_jira_id = st.text_input("JIRA ID (optional, leave empty to delete all edited test cases)", key="delete_edited_jira_id_input")

                    # Add some spacing
                    st.write("")

                    # Delete button - using the same simple approach as Delete by JIRA ID
                    if st.button("🗑️ Delete Edited Test Cases",
                               key="delete_edited_test_cases_button",
                               help="Delete test cases marked as edited (is_edited = 1)"):
                        if delete_edited_jira_id:
                            # Delete edited test cases for specific JIRA ID
                            with st.spinner("Deleting edited test cases..."):
                                success = db.delete_edited_test_cases(db.DATABASE_PATH, delete_edited_jira_id, admin_password)
                                if success:
                                    st.success(f"Successfully deleted edited test cases for {delete_edited_jira_id}")
                                else:
                                    st.error(f"Failed to delete edited test cases for {delete_edited_jira_id}. Admin password may be required.")
                        else:
                            # Delete all edited test cases
                            with st.spinner("Deleting all edited test cases..."):
                                success = db.delete_all_edited_test_cases(db.DATABASE_PATH, admin_password)
                                if success:
                                    st.success("Successfully deleted all edited test cases")
                                else:
                                    st.error("Failed to delete all edited test cases. Admin password may be required.")

                # Tab 3: Delete by Time Range
                with delete_tabs[2]:
                    st.info("Delete test cases created within a specific time range.")

                    # JIRA ID for deletion (optional)
                    delete_duplicate_jira_id = st.text_input("JIRA ID (optional, leave empty to delete all duplicate test cases)", key="delete_duplicate_jira_id_input")

                    # Add some spacing
                    st.write("")

                    # Delete button with confirmation
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        delete_duplicate_button = st.button("🗑️ Delete Duplicate Test Cases",
                                                        key="delete_duplicate_test_cases_button",
                                                        help="Delete duplicate test cases, keeping only the oldest version")

                    # Add confirmation checkbox
                    with col2:
                        if delete_duplicate_button:
                            confirm_delete_duplicate = st.checkbox("Confirm", key="delete_duplicate_confirm")

                    # Handle delete duplicate test cases button
                    if delete_duplicate_button:
                        if 'confirm_delete_duplicate' in locals() and confirm_delete_duplicate:
                            with st.spinner("Deleting duplicate test cases..."):
                                if delete_duplicate_jira_id:
                                    # Delete duplicate test cases for specific JIRA ID
                                    success = db.delete_duplicate_test_cases(db.DATABASE_PATH, delete_duplicate_jira_id)
                                    if success:
                                        st.success(f"Successfully deleted duplicate test cases for {delete_duplicate_jira_id}")
                                    else:
                                        st.error(f"Failed to delete duplicate test cases for {delete_duplicate_jira_id}")
                                else:
                                    # Delete all duplicate test cases
                                    success = db.delete_duplicate_test_cases(db.DATABASE_PATH)
                                    if success:
                                        st.success("Successfully deleted all duplicate test cases")
                                    else:
                                        st.error("Failed to delete duplicate test cases")
                        else:
                            st.error("Please confirm by checking the box to delete duplicate test cases")

                # Tab 4: Delete by Time Range
                with delete_tabs[2]:
                    st.info("Delete test cases created within a specific time range.")

                    # Date range selection
                    col1, col2 = st.columns(2)
                    with col1:
                        start_date = st.date_input("Start Date", key="delete_start_date")
                    with col2:
                        end_date = st.date_input("End Date", key="delete_end_date")

                    # Add some spacing
                    st.write("")

                    # Delete button with confirmation
                    col1, col2 = st.columns([3, 1])
                    with col1:
                        delete_time_button = st.button("🗑️ Delete Test Cases by Time Range",
                                                    key="delete_time_range_button",
                                                    help="Delete test cases created within the specified time range")

                    # Add confirmation checkbox
                    with col2:
                        if delete_time_button:
                            confirm_delete_time = st.checkbox("Confirm", key="delete_time_confirm")

                    # Handle delete by time range button
                    if delete_time_button:
                        if 'confirm_delete_time' in locals() and confirm_delete_time:
                            # Convert dates to datetime with time at start and end of day
                            start_datetime = datetime.combine(start_date, datetime.min.time())
                            end_datetime = datetime.combine(end_date, datetime.max.time())

                            if start_datetime <= end_datetime:
                                with st.spinner("Deleting test cases by time range..."):
                                    # Add function to delete test cases by time range
                                    success = db.delete_test_cases_by_time_range(db.DATABASE_PATH, start_datetime, end_datetime)
                                    if success:
                                        st.success(f"Successfully deleted test cases created between {start_date} and {end_date}")
                                    else:
                                        st.error(f"Failed to delete test cases by time range")
                            else:
                                st.error("Start date must be before or equal to end date")
                        else:
                            st.error("Please confirm by checking the box to delete test cases by time range")

                # Add a separator
                st.markdown("---")

                # Clear Entire Database section
                st.subheader("Clear Entire Database")
                st.warning("⚠️ This will delete ALL test cases, test runs, and JIRA issues in the database. This action cannot be undone.")

                # Clear button with confirmation
                col1, col2 = st.columns([3, 1])
                with col1:
                    clear_button = st.button("⚠️ Clear Entire Database",
                                            key="clear_database_button",
                                            help="This will delete ALL test cases in the database")

                # Add confirmation checkbox
                with col2:
                    if clear_button:
                        confirm_clear = st.checkbox("Confirm", key="clear_db_confirm")

                # Handle clear database button
                if clear_button:
                    if 'confirm_clear' in locals() and confirm_clear:
                        with st.spinner("Clearing database..."):
                            success = db.clear_database(db.DATABASE_PATH, admin_password)
                            if success:
                                st.success("Database cleared successfully")
                            else:
                                st.error("Failed to clear database. Admin password may be required.")
                    else:
                        st.error("Please confirm by checking the box to clear the database")

# Footer section
st.markdown("---")
st.markdown("### About")
st.markdown("GretahAI Test Case Generator uses AI to create test Cases from JIRA tickets")

# Initialize the page state if it doesn't exist
if "current_page" not in st.session_state:
    st.session_state["current_page"] = "generator"

# Main content based on current page
if st.session_state["current_page"] == "generator":
    # Test Case Generator Page
    # Main content area header
    st.markdown('<h1 class="main-header">🤖 GretahAI Test Case Generator</h1>', unsafe_allow_html=True)

    # Dynamic header based on provider
    if ai_provider == "Local":
         st.markdown(
            '<div style="text-align: center; background-color: #E3F2FD; border: 1px solid #2196F3; color: #1E88E5; padding: 5px; border-radius: 5px; margin-bottom: 20px;">'
            '⚙️ Using <strong>locally-hosted AI</strong> for private & secure processing'
            '</div>',
            unsafe_allow_html=True
        )
    else: # Google AI
         st.markdown(
            '<div style="text-align: center; background-color: #E8F5E9; border: 1px solid #4CAF50; color: #388E3C; padding: 5px; border-radius: 5px; margin-bottom: 20px;">'
            '☁️ Using <strong>Google AI Studio</strong> via API'
            '</div>',
            unsafe_allow_html=True
        )

    # Layout for input
    col1, col2 = st.columns([3, 1])
    with col1:
        case_id = st.text_input("Enter JIRA Case ID", "TP-1", key="jira_case_id_input") # Example key
        col_type, col_num = st.columns(2)
        with col_type:
            # Create a format function for test types
            def format_test_type(test_type):
                test_type_labels = {
                    "all": "All Test Case Types",
                    "positive": "Positive Scenarios",
                    "negative": "Negative Scenarios",
                    "security": "Security Testing",
                    "performance": "Performance Testing"
                }
                return test_type_labels.get(test_type, test_type)

            test_type = st.selectbox(
                "Test Case Type",
                options=["all", "positive", "negative", "security", "performance"],
                format_func=format_test_type,
                key="test_type_select"
            )
        with col_num:
            num_scenarios = st.number_input("Number of Test cases", min_value=1, max_value=20, value=5, key="num_scenarios_input") # Increased max

    with col2:
        # Disable button if JIRA is not connected or if Google API key is missing when needed
        disable_generate = not jira_connected or (ai_provider == "Google AI Studio" and not google_api_key)
        generate_button = st.button(
            "🚀 Generate Test Cases",
            use_container_width=True,
            disabled=disable_generate,
            key="generate_button"
            )
        if disable_generate:
            if not jira_connected:
                st.error("JIRA connection failed. Cannot generate.")
            elif ai_provider == "Google AI Studio" and not google_api_key:
                st.warning("Enter Google API Key to enable generation.")
else:
    # Test Analysis Page
    st.markdown('<h1 class="main-header">📊 Test Analysis Dashboard</h1>', unsafe_allow_html=True)

    # Check if the user is already logged in through the admin panel
    is_admin_logged_in = st.session_state.get("is_admin_logged_in", False)
    current_user = st.session_state.get("admin_username", "")

    # If the user is logged in as admin, use that username
    if is_admin_logged_in and not current_user:
        current_user = st.session_state.get("admin_user", "")
        # Store it in admin_username for consistency
        st.session_state["admin_username"] = current_user

    # If no user is logged in, show a message
    if not current_user:
        st.warning("Please log in through the Admin Panel to view your test runs.")

        # Add a button to navigate to the Admin Panel
        if st.button("Go to Admin Panel", key="test_analysis_admin_panel_button"):
            # Set a flag to automatically open the Admin Panel expander
            st.session_state["open_admin_panel"] = True
            st.rerun()
    else:
        # Show the user's test runs with a clear message
        st.info(f"Showing test runs for user: {current_user}")

        # Create tabs for different analysis functions
        analysis_tabs = st.tabs(["Test Runs", "Visualization", "Reporting"])

        # We'll handle the unwanted sections differently - by not rendering them at all

        # Tab 1: Test Runs - For viewing and editing test cases
        with analysis_tabs[0]:
            # Get the user's test runs from the database
            test_runs = db.get_test_runs_by_user(db.DATABASE_PATH, current_user)

            if test_runs:
                # Create a DataFrame from the test runs
                test_runs_df = pd.DataFrame(test_runs)

                # Format the timestamp column
                if "timestamp" in test_runs_df.columns:
                    test_runs_df["timestamp"] = pd.to_datetime(test_runs_df["timestamp"])
                    test_runs_df["timestamp"] = test_runs_df["timestamp"].dt.strftime("%Y-%m-%d %H:%M:%S")

                # Rename columns for better display
                test_runs_df = test_runs_df.rename(columns={
                    "id": "Run ID",
                    "jira_id": "JIRA ID",
                    "test_type": "Test Type",
                    "timestamp": "Timestamp",
                    "num_test_cases": "# Test Cases",
                    "status": "Status",
                    "notes": "Notes"
                })

                # Check if a test run is selected (from session state)
                selected_run_id = st.session_state.get("selected_test_run_id", None)

                # If no run is selected, show the list of test runs
                if not selected_run_id:
                    # Create a more visually appealing display for test runs
                    st.markdown("### Your Test Runs")

                    # Create a table with action buttons
                    # First, create a copy of the dataframe for display
                    display_runs_df = test_runs_df.copy()

                    # Add a view button column
                    display_runs_df["View"] = False

                    # Display the table with action buttons
                    edited_runs = st.data_editor(
                        display_runs_df,
                        column_config={
                            "Run ID": st.column_config.NumberColumn("Run ID", width="small"),
                            "JIRA ID": st.column_config.TextColumn("JIRA ID", width="small"),
                            "Test Type": st.column_config.TextColumn("Test Type", width="small"),
                            "Timestamp": st.column_config.DatetimeColumn("Created", width="medium", format="DD-MM-YYYY HH:mm"),
                            "# Test Cases": st.column_config.NumberColumn("# Test Cases", width="small"),
                            "Status": st.column_config.TextColumn("Status", width="small"),
                            "Notes": st.column_config.TextColumn("Notes", width="medium"),
                            "View": st.column_config.CheckboxColumn("View", width="small", help="Select to view test cases")
                        },
                        disabled=["Run ID", "JIRA ID", "Test Type", "Timestamp", "# Test Cases", "Status", "Notes"],
                        hide_index=True,
                        use_container_width=True,
                        key="test_runs_table"
                    )

                    # Check if any run is selected for viewing
                    if edited_runs["View"].any():
                        # Get the first selected run
                        selected_row = edited_runs[edited_runs["View"]].iloc[0]
                        # Store the selected run ID in session state
                        st.session_state["selected_test_run_id"] = selected_row["Run ID"]
                        # Don't rerun the app, just continue with the selected run
                        selected_run_id = selected_row["Run ID"]

                    # Add a section with cards for quick access
                    st.markdown("### Recent Test Runs")

                    # Sort test runs by timestamp (newest first)
                    if "Timestamp" in test_runs_df.columns:
                        # Convert to datetime for proper sorting
                        test_runs_df["Timestamp_dt"] = pd.to_datetime(test_runs_df["Timestamp"])
                        # Sort by timestamp (descending)
                        sorted_runs = test_runs_df.sort_values(by="Timestamp_dt", ascending=False)
                    else:
                        # Fallback if timestamp column is not available
                        sorted_runs = test_runs_df.copy()

                    # Get the 3 most recent for the cards
                    recent_runs = sorted_runs.head(3)

                    # Create a card-based layout for recent runs
                    cols = st.columns(3)
                    for i, (_, run) in enumerate(recent_runs.iterrows()):
                        with cols[i]:
                            with st.container(border=True):
                                st.markdown(f"#### {run['JIRA ID']} - {run['Test Type']}")
                                st.markdown(f"**Created:** {run['Timestamp']}")
                                # Get the actual count of test cases for this run
                                try:
                                    # Connect to the database
                                    conn = sqlite3.connect(db.DATABASE_PATH)
                                    conn.row_factory = sqlite3.Row
                                    cursor = conn.cursor()

                                    # Get the JIRA ID and test type for this run
                                    jira_id = run['JIRA ID']
                                    test_type = run['Test Type']

                                    # Count unique test case IDs for this JIRA ID and test type
                                    cursor.execute(
                                        """
                                        SELECT COUNT(DISTINCT test_case_id)
                                        FROM test_cases
                                        WHERE jira_id = ? AND dashboard_test_type = ? AND test_case_id LIKE 'TC_%'
                                        """,
                                        (jira_id, test_type)
                                    )

                                    actual_count = cursor.fetchone()[0]
                                    conn.close()

                                    # Update the run count in the dataframe
                                    run['# Test Cases'] = actual_count

                                    # Display the actual count
                                    st.markdown(f"**Test Cases:** {actual_count}")
                                except Exception as e:
                                    # If there's an error, just display the original count
                                    st.markdown(f"**Test Cases:** {run['# Test Cases']}")

                                # Add a button to view this test run (without page refresh)
                                if st.button(f"View Test Cases", key=f"view_run_{run['Run ID']}", use_container_width=True):
                                    st.session_state["selected_test_run_id"] = run['Run ID']
                                    # Use rerun to avoid page refresh
                                    st.rerun()

                    # Add a section for all test runs with a selectbox
                    st.markdown("### All Test Runs")

                    # Create a selectbox with all test runs
                    if not sorted_runs.empty:
                        # Create a list of options for the selectbox
                        run_options = [f"Run {run['Run ID']}: {run['JIRA ID']} - {run['Test Type']} ({run['Timestamp']})" for _, run in sorted_runs.iterrows()]
                        run_ids = sorted_runs["Run ID"].tolist()

                        # Create a mapping from option string to run ID
                        option_to_run_id = dict(zip(run_options, run_ids))

                        # Create the selectbox
                        selected_option = st.selectbox("Select a test run", run_options, key="all_test_runs_selectbox")

                        # Add a button to view the selected test run
                        if st.button("View Selected Test Run", key="view_selected_run", use_container_width=True):
                            selected_run_id = option_to_run_id[selected_option]
                            st.session_state["selected_test_run_id"] = selected_run_id
                            # Use rerun to avoid page refresh
                            st.rerun()

                if selected_run_id:
                    # Show a back button to return to the test run list
                    if st.button("← Back to Test Runs", key="back_to_test_runs"):
                        st.session_state.pop("selected_test_run_id", None)
                        # Use rerun to avoid page refresh
                        st.rerun()
                    else:
                        # Get the test cases for the selected test run
                        test_cases = db.get_test_cases_by_test_run(db.DATABASE_PATH, selected_run_id)

                        # Convert to DataFrame if it's a list
                        if isinstance(test_cases, list) and len(test_cases) > 0:
                            test_cases_df = pd.DataFrame(test_cases)
                        elif hasattr(test_cases, 'empty') and not test_cases.empty:
                            test_cases_df = test_cases
                        else:
                            # Try a different approach - get test cases directly from the database
                            # Get the JIRA ID and test type from the test run
                            run_info = test_runs_df[test_runs_df["Run ID"] == selected_run_id]
                            if not run_info.empty:
                                jira_id = run_info["JIRA ID"].iloc[0]
                                test_type = run_info["Test Type"].iloc[0]

                                # Get test cases for this JIRA ID and test type
                                test_cases_df = db.get_test_cases_from_database(db.DATABASE_PATH, jira_id, test_type, user_name=current_user)

                                # Filter to only include test cases for this test run
                                if not test_cases_df.empty and "Test Run ID" in test_cases_df.columns:
                                    test_cases_df = test_cases_df[test_cases_df["Test Run ID"] == selected_run_id]
                            else:
                                test_cases_df = pd.DataFrame()

                        # Always try to get test steps directly from the database
                        try:
                            # Get the JIRA ID and test type from the test run
                            run_info = test_runs_df[test_runs_df["Run ID"] == selected_run_id]
                            if not run_info.empty:
                                jira_id = run_info["JIRA ID"].iloc[0]
                                test_type = run_info["Test Type"].iloc[0]

                                # Get test cases with steps for this JIRA ID and test type
                                conn = sqlite3.connect(db.DATABASE_PATH)
                                conn.row_factory = sqlite3.Row
                                cursor = conn.cursor()

                                # Get all test cases and steps for this test run
                                # First, get the latest timestamp for each test case to avoid duplicates
                                query = """
                                WITH latest_test_cases AS (
                                    SELECT tc.id, tc.test_case_id, MAX(tc.timestamp) as latest_timestamp
                                    FROM test_cases tc
                                    JOIN test_runs tr ON tc.jira_id = tr.jira_id AND tc.dashboard_test_type = tr.test_type
                                    WHERE tr.id = ?
                                    GROUP BY tc.test_case_id
                                )
                                SELECT
                                    tc.id as test_case_db_id,
                                    tc.jira_id as "User Story ID",
                                    tc.test_case_id as "Test Case ID",
                                    tc.test_case_objective as "Test Case Objective",
                                    tc.prerequisite as "Prerequisite",
                                    tc.priority as "Priority",
                                    tc.test_type as "Test Type",
                                    tc.test_group as "Test Group",
                                    tc.project as "Project",
                                    tc.feature as "Feature",
                                    tc.timestamp as "Timestamp",
                                    tc.dashboard_test_type as "Dashboard Test Type",
                                    tc.user_name as "User Name",
                                    tc.is_edited as "Is Edited",
                                    ts.step_number as "Step No",
                                    ts.test_step as "Test Steps",
                                    ts.expected_result as "Expected Result",
                                    ts.actual_result as "Actual Result",
                                    ts.test_status as "Test Status",
                                    ts.defect_id as "Defect ID",
                                    ts.comments as "Comments",
                                    tr.id as "Test Run ID"
                                FROM test_cases tc
                                JOIN latest_test_cases ltc ON tc.id = ltc.id
                                JOIN test_runs tr ON tc.jira_id = tr.jira_id AND tc.dashboard_test_type = tr.test_type
                                LEFT JOIN test_steps ts ON tc.id = ts.test_case_id
                                WHERE tr.id = ?
                                ORDER BY tc.test_case_id, ts.step_number
                                """
                                cursor.execute(query, (selected_run_id, selected_run_id))
                                rows = cursor.fetchall()

                                # Convert rows to dictionaries
                                test_cases_with_steps = []

                                # Track test cases we've already processed to avoid duplicates
                                processed_test_cases = {}

                                for row in rows:
                                    test_case = dict(row)
                                    test_case_id = test_case["Test Case ID"]
                                    step_no = test_case.get("Step No", "")

                                    # If this is a header row (no step number) or the first step of a test case
                                    if not step_no or (test_case_id not in processed_test_cases):
                                        # Add the full test case header
                                        processed_test_cases[test_case_id] = True

                                        # Create a header row with all test case information
                                        header_row = {
                                            "Timestamp": test_case["Timestamp"],
                                            "Project": test_case["Project"],
                                            "Feature": test_case["Feature"],
                                            "User Story ID": test_case["User Story ID"],
                                            "Test Case ID": test_case_id,
                                            "Test Case Objective": test_case["Test Case Objective"],
                                            "Prerequisite": test_case["Prerequisite"],
                                            "Step No": "",  # Empty for header
                                            "Test Steps": "",  # Empty for header
                                            "Expected Result": "",  # Empty for header
                                            "Actual Result": "",
                                            "Test Status": "",
                                            "Priority": test_case["Priority"],
                                            "Defect ID": "",
                                            "Comments": "",
                                            "Test Type": test_case["Test Type"],
                                            "Test Group": test_case["Test Group"],
                                            "User Name": test_case["User Name"],
                                            "Dashboard Test Type": test_case["Dashboard Test Type"],
                                            "Test Run ID": test_case["Test Run ID"],
                                            "Is Edited": test_case["Is Edited"]
                                        }
                                        test_cases_with_steps.append(header_row)

                                    # If this row has a step number, add it as a step
                                    if step_no:
                                        step_row = {
                                            "Timestamp": "",  # Empty for steps
                                            "Project": "",  # Empty for steps
                                            "Feature": "",  # Empty for steps
                                            "User Story ID": "",  # Empty for steps
                                            "Test Case ID": "",  # Empty for steps
                                            "Test Case Objective": "",  # Empty for steps
                                            "Prerequisite": "",  # Empty for steps
                                            "Step No": step_no,
                                            "Test Steps": test_case["Test Steps"],
                                            "Expected Result": test_case["Expected Result"],
                                            "Actual Result": test_case["Actual Result"],
                                            "Test Status": test_case["Test Status"],
                                            "Priority": "",  # Empty for steps
                                            "Defect ID": test_case["Defect ID"],
                                            "Comments": test_case["Comments"],
                                            "Test Type": "",  # Empty for steps
                                            "Test Group": "",  # Empty for steps
                                            "User Name": "",  # Empty for steps
                                            "Dashboard Test Type": "",  # Empty for steps
                                            "Test Run ID": test_case["Test Run ID"],
                                            "Is Edited": ""  # Empty for steps
                                        }
                                        test_cases_with_steps.append(step_row)

                                # Convert to DataFrame
                                if test_cases_with_steps:
                                    test_cases_df = pd.DataFrame(test_cases_with_steps)
                                    st.success(f"Successfully retrieved {len(test_cases_df)} rows with test steps")
                                else:
                                    st.warning(f"No test cases found for test run {selected_run_id}")

                                conn.close()
                        except Exception as e:
                            st.error(f"Error getting test steps: {e}")

                        # Debug information (commented out for cleaner UI)
                        # st.write(f"Found {len(test_cases_df)} rows for test run {selected_run_id}")

                        # Check if we have test steps
                        if "Step No" not in test_cases_df.columns or test_cases_df["Step No"].isna().all():
                            st.warning("No test steps found for this test run. The test cases may not have been properly generated.")

                        if not test_cases_df.empty:
                            # Get the JIRA ID and test type from the test run
                            run_info = test_runs_df[test_runs_df["Run ID"] == selected_run_id]
                            jira_id = run_info["JIRA ID"].iloc[0] if not run_info.empty else "Unknown"
                            test_type = run_info["Test Type"].iloc[0] if not run_info.empty else "Unknown"

                            # Display the test cases with a nicer header
                            st.markdown(f"""
                            <div style="background-color: #f0f2f6; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                                <h2 style="margin: 0; color: #1E88E5;">Test Cases for {jira_id}</h2>
                                <p style="margin: 5px 0 0 0;">Run ID: {selected_run_id} | Type: {test_type}</p>
                            </div>
                            """, unsafe_allow_html=True)

                            # Ensure User Story ID and Step No are treated as strings
                            if "User Story ID" in test_cases_df.columns:
                                test_cases_df["User Story ID"] = test_cases_df["User Story ID"].fillna("").astype(str)
                            if "Step No" in test_cases_df.columns:
                                test_cases_df["Step No"] = test_cases_df["Step No"].astype(str)

                            # Get unique test case IDs and count
                            unique_test_cases = []
                            if "Test Case ID" in test_cases_df.columns:
                                # Filter out empty strings and count only valid test case IDs (TC_XXX format)
                                valid_test_case_ids = test_cases_df['Test Case ID'].dropna().astype(str)
                                valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                                # Only count IDs that match the TC_XXX pattern
                                valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                                # Get unique IDs
                                unique_test_cases = valid_test_case_ids.unique()

                                # Debug the count
                                print(f"Found {len(unique_test_cases)} unique test cases: {unique_test_cases}")

                            # Create a summary section with metrics
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                st.metric("Total Test Cases", len(unique_test_cases))
                            with col2:
                                # Count test cases by status if available
                                if "Test Status" in test_cases_df.columns:
                                    passed = len(test_cases_df[test_cases_df["Test Status"] == "Pass"])
                                    st.metric("Passed Tests", passed, delta=f"{passed}/{len(unique_test_cases)}")
                                else:
                                    st.metric("Test Type", test_type.capitalize())
                            with col3:
                                # Count test cases by priority if available
                                if "Priority" in test_cases_df.columns:
                                    high_priority = len(test_cases_df[test_cases_df["Priority"] == "High"])
                                    st.metric("High Priority", high_priority, delta=f"{high_priority}/{len(unique_test_cases)}")
                                else:
                                    st.metric("JIRA ID", jira_id)

                        # Updated column configuration to match the desired order with Timestamp as first column
                        column_config = {
                            "Timestamp": st.column_config.TextColumn(width="medium"),  # Add Timestamp column
                            "Project": st.column_config.TextColumn(width="small"),
                            "Feature": st.column_config.TextColumn(width="medium"),
                            "User Story ID": st.column_config.Column(width="small", required=True),  # Use generic Column type
                            "Test Case ID": st.column_config.TextColumn(width="small"),
                            "Test Case Objective": st.column_config.TextColumn(width="medium"),
                            "Prerequisite": st.column_config.TextColumn(width="medium"),
                            "Step No": st.column_config.TextColumn(width="small"),
                            "Test Steps": st.column_config.TextColumn(width="large"),
                            "Expected Result": st.column_config.TextColumn(width="large"),
                            "Actual Result": st.column_config.TextColumn(width="small", default=""),
                            "Test Status": st.column_config.SelectboxColumn(width="small", options=["Pass", "Fail", "Blocked", "Not Run"], default="Not Run"),
                            "Priority": st.column_config.SelectboxColumn(width="small", options=["High", "Medium", "Low"], default="Medium"),
                            "Defect ID": st.column_config.TextColumn(width="small", default=""),
                            "Comments": st.column_config.TextColumn(width="large", default=""),
                            "Test Type": st.column_config.TextColumn(width="small"),    # Test type
                            "Test Group": st.column_config.TextColumn(width="medium"),  # Group identifier
                            "User Name": st.column_config.TextColumn(width="small"),    # User who generated the test case
                            "Dashboard Test Type": st.column_config.TextColumn(width="small")  # Dashboard test type
                        }

                        # Ensure all columns in config exist in df
                        for col in column_config.keys():
                            if col not in test_cases_df.columns:
                                test_cases_df[col] = "" # Add missing column with default value

                        # Reorder columns based on config
                        ordered_columns = [col for col in column_config.keys() if col in test_cases_df.columns]
                        ordered_columns += [col for col in test_cases_df.columns if col not in ordered_columns]
                        test_cases_df = test_cases_df[ordered_columns]

                        # Process the dataframe to group test steps with their parent test cases
                        # This creates a more readable format similar to the Generated Test Cases tab

                        # First, create a copy of the dataframe to avoid modifying the original
                        display_df = test_cases_df.copy()

                        # First, identify all unique test cases
                        unique_test_cases = display_df['Test Case ID'].dropna().unique()

                        # For each test case, make sure only the first row has the header information
                        for test_case in unique_test_cases:
                            # Get all rows for this test case
                            test_case_rows = display_df[display_df['Test Case ID'] == test_case].index.tolist()

                            if len(test_case_rows) > 1:
                                # The first row should have step number 1
                                first_row = None
                                for idx in test_case_rows:
                                    step_no = display_df.loc[idx, 'Step No']
                                    if pd.notna(step_no) and (step_no == 1 or step_no == '1'):
                                        first_row = idx
                                        break

                                # If we found the first row, clear header info from all other rows
                                if first_row is not None:
                                    for idx in test_case_rows:
                                        if idx != first_row:
                                            # Clear fields that should only appear in the test case header
                                            display_df.loc[idx, 'Timestamp'] = ''
                                            display_df.loc[idx, 'Project'] = ''
                                            display_df.loc[idx, 'Feature'] = ''
                                            display_df.loc[idx, 'User Story ID'] = ''
                                            display_df.loc[idx, 'Test Case ID'] = ''
                                            display_df.loc[idx, 'Test Case Objective'] = ''
                                            display_df.loc[idx, 'Prerequisite'] = ''
                                            display_df.loc[idx, 'Priority'] = ''
                                            display_df.loc[idx, 'Test Type'] = ''
                                            display_df.loc[idx, 'Test Group'] = ''
                                            display_df.loc[idx, 'User Name'] = ''
                                            display_df.loc[idx, 'Dashboard Test Type'] = ''
                                            # Keep the Test Run ID field for reference

                        # Create a tabbed view with both table and expandable formats
                        if "Test Case ID" in test_cases_df.columns:
                            # Get unique test case IDs
                            unique_ids = test_cases_df["Test Case ID"].dropna().unique()

                            # Create a container for all test cases
                            with st.container():
                                # Add a filter for test case types
                                if "Test Type" in test_cases_df.columns:
                                    # Get unique test types, filter out empty values, and sort
                                    test_types = []

                                    # Get only the header rows (rows with a Test Case ID)
                                    header_rows = test_cases_df[test_cases_df["Test Case ID"].notna() & (test_cases_df["Test Case ID"] != "")]

                                    # Get unique test types from header rows
                                    if not header_rows.empty and "Test Type" in header_rows.columns:
                                        test_types = header_rows["Test Type"].dropna().unique().tolist()
                                        # Filter out empty strings and None values
                                        test_types = [t for t in test_types if t and isinstance(t, str) and t.strip()]
                                        # Sort the list
                                        test_types = sorted(test_types)

                                    # If no test types found, use a default
                                    if not test_types:
                                        test_types = ["POSITIVE"]

                                    # Add "All" at the beginning
                                    test_types = ["All"] + test_types

                                    # Debug the test types
                                    print(f"Test types after filtering: {test_types}")

                                    # Create the selectbox
                                    selected_type = st.selectbox("Filter by Test Type", test_types, key="test_run_type_filter")

                                # Add a search box for test case objectives
                                search_term = st.text_input("Search in Test Case Objectives", key="test_case_search")

                                # Filter test cases based on selections
                                filtered_ids = unique_ids
                                if selected_type != "All" and "Test Type" in test_cases_df.columns:
                                    # Get test cases of the selected type
                                    type_filter = test_cases_df[test_cases_df["Test Type"] == selected_type]["Test Case ID"].unique()
                                    filtered_ids = [tc_id for tc_id in filtered_ids if tc_id in type_filter]

                                if search_term:
                                    # Filter by search term in objective
                                    search_filter = test_cases_df[test_cases_df["Test Case Objective"].str.contains(search_term, case=False, na=False)]["Test Case ID"].unique()
                                    filtered_ids = [tc_id for tc_id in filtered_ids if tc_id in search_filter]

                                # Filter out any empty test case IDs and the ":" entry
                                filtered_ids = [tc_id for tc_id in filtered_ids if tc_id and isinstance(tc_id, str) and tc_id.strip() and tc_id != ":"]
                                unique_ids = [tc_id for tc_id in unique_ids if tc_id and isinstance(tc_id, str) and tc_id.strip() and tc_id != ":"]

                                # Debug the filtered IDs
                                print(f"Filtered IDs: {filtered_ids}")
                                print(f"Unique IDs: {unique_ids}")

                                # Show count of filtered test cases
                                st.info(f"Showing {len(filtered_ids)} of {len(unique_ids)} test cases")

                                # Create tabs for different views
                                view_tabs = st.tabs(["Table View", "Expandable View"])

                                # Tab 1: Table View - Similar to Generated Test Cases tab
                                with view_tabs[0]:
                                    # Filter the dataframe to only include the filtered test cases and their steps
                                    filtered_df = pd.DataFrame()

                                    # For each test case ID, get all rows (header and steps)
                                    for tc_id in filtered_ids:
                                        # Get the header row for this test case
                                        header_rows = test_cases_df[test_cases_df["Test Case ID"] == tc_id]

                                        if not header_rows.empty:
                                            # Add the header row to the filtered dataframe
                                            header_row = header_rows.iloc[0:1]
                                            filtered_df = pd.concat([filtered_df, header_row])

                                            # Get the steps for this test case directly from the database
                                            try:
                                                # Connect to the database
                                                conn = sqlite3.connect(db.DATABASE_PATH)
                                                conn.row_factory = sqlite3.Row
                                                cursor = conn.cursor()

                                                # First get the test case database ID
                                                cursor.execute(
                                                    """
                                                    SELECT id FROM test_cases
                                                    WHERE test_case_id = ?
                                                    ORDER BY timestamp DESC
                                                    LIMIT 1
                                                    """,
                                                    (tc_id,)
                                                )

                                                result = cursor.fetchone()
                                                if result:
                                                    test_case_db_id = result[0]

                                                    # Now get the steps for this test case
                                                    cursor.execute(
                                                        """
                                                        SELECT
                                                            step_number as "Step No",
                                                            test_step as "Test Steps",
                                                            expected_result as "Expected Result",
                                                            actual_result as "Actual Result",
                                                            test_status as "Test Status",
                                                            defect_id as "Defect ID",
                                                            comments as "Comments"
                                                        FROM test_steps
                                                        WHERE test_case_id = ?
                                                        ORDER BY step_number
                                                        """,
                                                        (test_case_db_id,)
                                                    )

                                                    # Convert to DataFrame
                                                    steps = cursor.fetchall()
                                                    if steps:
                                                        columns = [description[0] for description in cursor.description]
                                                        steps_df = pd.DataFrame(steps, columns=columns)
                                                        steps_df = steps_df.fillna("")

                                                        # Create empty rows for all other columns
                                                        for col in test_cases_df.columns:
                                                            if col not in steps_df.columns:
                                                                steps_df[col] = ""

                                                        # Add the steps to the filtered dataframe
                                                        filtered_df = pd.concat([filtered_df, steps_df])

                                                conn.close()
                                            except Exception as e:
                                                print(f"Error getting steps for {tc_id}: {e}")
                                                # If we can't get steps from the database, try to get them from the dataframe
                                                step_rows = test_cases_df[
                                                    (test_cases_df["Test Case ID"] == "") &
                                                    (test_cases_df["Step No"] != "")
                                                ]
                                                if not step_rows.empty:
                                                    filtered_df = pd.concat([filtered_df, step_rows])

                                    # If the filtered dataframe is empty, use the original filter
                                    if filtered_df.empty:
                                        filtered_df = test_cases_df[test_cases_df["Test Case ID"].isin(filtered_ids)]

                                    # Debug
                                    print(f"Filtered dataframe has {len(filtered_df)} rows")

                                    # Apply styling to match Excel format
                                    styled_df = filtered_df.style.apply(lambda x: [
                                        'background-color: #E3F2FD' if x.name == 0 or
                                                                     (pd.notna(x['Test Case ID']) and x['Test Case ID'] != '') else
                                        '' for _ in x
                                    ], axis=1)

                                    # Apply additional styling
                                    styled_df = styled_df.set_properties(**{
                                        'text-align': 'center',
                                        'font-family': 'Calibri',
                                        'font-size': '11pt',
                                        'border': '1px solid #BDBDBD'
                                    })

                                    # Display the data editor with styling
                                    edited_df = st.data_editor(
                                        filtered_df,
                                        use_container_width=True,
                                        column_config=column_config,
                                        hide_index=True,
                                        num_rows="dynamic",
                                        height=500,
                                        key="test_run_data_editor"
                                    )

                                    # Add a button to export the test cases to Excel
                                    if st.button("Export to Excel", key="test_run_export_button"):
                                        try:
                                            # Create a temporary Excel file with the test cases
                                            test_cases_dir = Path("Test_cases")
                                            test_cases_dir.mkdir(exist_ok=True)
                                            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                                            # Get the JIRA ID and test type from the test run
                                            run_info = test_runs_df[test_runs_df["Run ID"] == selected_run_id]
                                            jira_id = run_info["JIRA ID"].iloc[0] if not run_info.empty else "Unknown"
                                            test_type = run_info["Test Type"].iloc[0] if not run_info.empty else "Unknown"

                                            temp_file = f"TestRun_{selected_run_id}_{jira_id}_{test_type}_{timestamp}.xlsx"
                                            temp_file_path = os.path.join(test_cases_dir, temp_file)

                                            # Export the edited dataframe to Excel
                                            create_formatted_excel_from_scenarios(
                                                edited_df,
                                                temp_file_path,
                                                is_dataframe=True,
                                                save_to_db=False,
                                                create_excel=True
                                            )

                                            # Store the path in session state for the download button
                                            st.session_state["test_run_excel_path"] = temp_file_path
                                            st.session_state["test_run_excel_filename"] = temp_file

                                            st.success(f"Excel file prepared successfully: {temp_file}")
                                        except Exception as e:
                                            st.error(f"Error preparing Excel file: {str(e)}")

                                    # Show download button if the Excel file has been prepared
                                    if "test_run_excel_path" in st.session_state and os.path.exists(st.session_state["test_run_excel_path"]):
                                        with open(st.session_state["test_run_excel_path"], "rb") as file:
                                            st.download_button(
                                                label="Download Excel File",
                                                data=file,
                                                file_name=st.session_state["test_run_excel_filename"],
                                                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                                key="test_run_download_button"
                                            )

                                # Tab 2: Expandable View
                                with view_tabs[1]:
                                    # Create an expander for each test case
                                    for tc_id in filtered_ids:
                                        # Skip empty test case IDs
                                        if not tc_id or not isinstance(tc_id, str) or not tc_id.strip():
                                            print(f"Skipping empty test case ID: {tc_id}")
                                            continue

                                        # Get all rows for this test case
                                        tc_rows = test_cases_df[test_cases_df["Test Case ID"] == tc_id]
                                        if tc_rows.empty:
                                            print(f"No rows found for test case ID: {tc_id}")
                                            continue

                                        # Get the header row (first row with this test case ID)
                                        header_rows = tc_rows[tc_rows["Test Case ID"] == tc_id]
                                        if header_rows.empty:
                                            continue

                                        # Get the first row that has the test case ID
                                        header_row = header_rows.iloc[0]

                                        # Debug the header row
                                        print(f"Header row for {tc_id}: {header_row['Test Case Objective']}")

                                        # Create an expander for this test case
                                        with st.expander(f"{tc_id}: {header_row.get('Test Case Objective', 'No objective')}"):
                                            # Create two columns for the header information
                                            col1, col2 = st.columns([2, 1])

                                            # Get values with fallbacks
                                            objective = header_row.get('Test Case Objective', '')
                                            jira_id = header_row.get('User Story ID', 'Unknown')
                                            feature = header_row.get('Feature', 'Unknown')
                                            prerequisite = header_row.get('Prerequisite', 'None')
                                            priority = header_row.get('Priority', 'Medium')
                                            test_type = header_row.get('Test Type', 'Unknown')
                                            test_group = header_row.get('Test Group', 'Unknown')
                                            timestamp = header_row.get('Timestamp', 'Unknown')

                                            # Debug the values
                                            print(f"Values for {tc_id}: JIRA={jira_id}, Feature={feature}, Type={test_type}")

                                            with col1:
                                                # Display test case header information
                                                st.markdown(f"""
                                                <div style="margin-bottom: 15px;">
                                                    <h4 style="margin: 0;">{tc_id}: {objective}</h4>
                                                    <p><strong>JIRA:</strong> {jira_id} |
                                                       <strong>Feature:</strong> {feature}</p>
                                                    <p><strong>Prerequisite:</strong> {prerequisite}</p>
                                                </div>
                                                """, unsafe_allow_html=True)

                                            with col2:
                                                # Display metadata in a styled box
                                                st.markdown(f"""
                                                <div style="background-color: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 15px;">
                                                    <p style="margin: 0;"><strong>Priority:</strong> {priority}</p>
                                                    <p style="margin: 0;"><strong>Type:</strong> {test_type}</p>
                                                    <p style="margin: 0;"><strong>Group:</strong> {test_group}</p>
                                                    <p style="margin: 0;"><strong>Created:</strong> {timestamp}</p>
                                                </div>
                                                """, unsafe_allow_html=True)

                                            # Filter out rows that have step numbers (exclude header rows)
                                            step_rows = tc_rows[tc_rows["Step No"].notna() & (tc_rows["Step No"] != "")]

                                            # If we don't have steps, try to get them directly from the database
                                            if step_rows.empty:
                                                try:
                                                    # Get test case ID and database ID
                                                    test_case_id = header_row.get("Test Case ID")

                                                    # Debug
                                                    print(f"Trying to get steps for {test_case_id} directly from database")

                                                    # Connect to the database
                                                    conn = sqlite3.connect(db.DATABASE_PATH)
                                                    conn.row_factory = sqlite3.Row
                                                    cursor = conn.cursor()

                                                    # First get the test case database ID
                                                    cursor.execute(
                                                        """
                                                        SELECT id FROM test_cases
                                                        WHERE test_case_id = ?
                                                        ORDER BY timestamp DESC
                                                        LIMIT 1
                                                        """,
                                                        (test_case_id,)
                                                    )

                                                    result = cursor.fetchone()
                                                    if result:
                                                        test_case_db_id = result[0]
                                                        print(f"Found test case database ID: {test_case_db_id}")

                                                        # Now get the steps for this test case
                                                        cursor.execute(
                                                            """
                                                            SELECT
                                                                step_number as "Step No",
                                                                test_step as "Test Steps",
                                                                expected_result as "Expected Result",
                                                                actual_result as "Actual Result",
                                                                test_status as "Test Status",
                                                                defect_id as "Defect ID",
                                                                comments as "Comments"
                                                            FROM test_steps
                                                            WHERE test_case_id = ?
                                                            ORDER BY step_number
                                                            """,
                                                            (test_case_db_id,)
                                                        )

                                                        # Convert to DataFrame
                                                        steps = cursor.fetchall()
                                                        if steps:
                                                            print(f"Found {len(steps)} steps for test case {test_case_id}")
                                                            columns = [description[0] for description in cursor.description]
                                                            step_rows = pd.DataFrame(steps, columns=columns)
                                                            step_rows = step_rows.fillna("")
                                                        else:
                                                            print(f"No steps found for test case {test_case_id}")

                                                            # Try a different approach - get all steps for this test case ID
                                                            cursor.execute(
                                                                """
                                                                SELECT
                                                                    ts.step_number as "Step No",
                                                                    ts.test_step as "Test Steps",
                                                                    ts.expected_result as "Expected Result",
                                                                    ts.actual_result as "Actual Result",
                                                                    ts.test_status as "Test Status",
                                                                    ts.defect_id as "Defect ID",
                                                                    ts.comments as "Comments"
                                                                FROM test_steps ts
                                                                JOIN test_cases tc ON ts.test_case_id = tc.id
                                                                WHERE tc.test_case_id = ?
                                                                ORDER BY ts.step_number
                                                                """,
                                                                (test_case_id,)
                                                            )

                                                            steps = cursor.fetchall()
                                                            if steps:
                                                                print(f"Found {len(steps)} steps using alternative query for test case {test_case_id}")
                                                                columns = [description[0] for description in cursor.description]
                                                                step_rows = pd.DataFrame(steps, columns=columns)
                                                                step_rows = step_rows.fillna("")
                                                    else:
                                                        print(f"No test case found with ID {test_case_id}")

                                                    conn.close()
                                                except Exception as e:
                                                    print(f"Error retrieving test steps: {e}")
                                                    st.error(f"Error retrieving test steps: {e}")

                                            # Check if we have any steps after trying to get them from the database
                                            if step_rows.empty:
                                                st.warning("No test steps found for this test case.")
                                            else:
                                                # Create a table for the test steps
                                                steps_df = step_rows[["Step No", "Test Steps", "Expected Result"]].copy()

                                                # Add status columns if they exist
                                                status_columns = ["Test Status", "Actual Result", "Defect ID", "Comments"]
                                                for col in status_columns:
                                                    if col in step_rows.columns:
                                                        steps_df[col] = step_rows[col]

                                                # Display the steps in a clean table with alternating row colors
                                                st.markdown("<h5>Test Steps:</h5>", unsafe_allow_html=True)

                                                # Create a styled HTML table for better appearance
                                                html_table = "<table style='width:100%; border-collapse: collapse;'>"

                                                # Add table header
                                                html_table += "<thead style='background-color: #f0f2f6;'><tr>"
                                                html_table += "<th style='padding: 8px; text-align: left; border-bottom: 1px solid #ddd;'>Step</th>"
                                                html_table += "<th style='padding: 8px; text-align: left; border-bottom: 1px solid #ddd;'>Action</th>"
                                                html_table += "<th style='padding: 8px; text-align: left; border-bottom: 1px solid #ddd;'>Expected Result</th>"

                                                # Add status column if it exists
                                                if "Test Status" in steps_df.columns:
                                                    html_table += "<th style='padding: 8px; text-align: left; border-bottom: 1px solid #ddd;'>Status</th>"

                                                html_table += "</tr></thead><tbody>"

                                                # Add table rows with alternating colors
                                                for i, (_, step) in enumerate(steps_df.iterrows()):
                                                    row_style = "background-color: #f9f9f9;" if i % 2 == 0 else ""
                                                    html_table += f"<tr style='{row_style}'>"
                                                    html_table += f"<td style='padding: 8px; text-align: left; border-bottom: 1px solid #ddd;'>{step['Step No']}</td>"
                                                    html_table += f"<td style='padding: 8px; text-align: left; border-bottom: 1px solid #ddd;'>{step['Test Steps']}</td>"
                                                    html_table += f"<td style='padding: 8px; text-align: left; border-bottom: 1px solid #ddd;'>{step['Expected Result']}</td>"

                                                    # Add status with color coding if it exists
                                                    if "Test Status" in steps_df.columns:
                                                        status = step['Test Status']
                                                        status_color = "#9E9E9E"  # Default gray
                                                        if status == "Pass":
                                                            status_color = "#4CAF50"  # Green
                                                        elif status == "Fail":
                                                            status_color = "#F44336"  # Red
                                                        elif status == "Blocked":
                                                            status_color = "#FF9800"  # Orange

                                                        html_table += f"<td style='padding: 8px; text-align: left; border-bottom: 1px solid #ddd;'>"
                                                        html_table += f"<span style='color: {status_color}; font-weight: bold;'>{status}</span>"
                                                        html_table += "</td>"

                                                    html_table += "</tr>"

                                                html_table += "</tbody></table>"

                                                # Display the HTML table
                                                st.markdown(html_table, unsafe_allow_html=True)

                                                # Add a section for test execution if status columns exist
                                                if "Test Status" in step_rows.columns:
                                                    # Create a divider
                                                    st.markdown("<hr style='margin: 15px 0;'>", unsafe_allow_html=True)
                                                    st.markdown("#### Test Execution Details")

                                                    # Create an editable dataframe for test execution
                                                    execution_df = step_rows[["Step No", "Test Steps", "Test Status"]].copy()
                                                    if "Actual Result" in step_rows.columns:
                                                        execution_df["Actual Result"] = step_rows["Actual Result"]
                                                    if "Defect ID" in step_rows.columns:
                                                        execution_df["Defect ID"] = step_rows["Defect ID"]
                                                    if "Comments" in step_rows.columns:
                                                        execution_df["Comments"] = step_rows["Comments"]

                                                    # Display the editable dataframe
                                                    st.dataframe(
                                                        execution_df,
                                                        use_container_width=True,
                                                        hide_index=True,
                                                        column_config={
                                                            "Step No": st.column_config.TextColumn("Step", width="small"),
                                                            "Test Steps": st.column_config.TextColumn("Action", width="medium"),
                                                            "Test Status": st.column_config.SelectboxColumn(
                                                                "Status",
                                                                width="small",
                                                                options=["Pass", "Fail", "Blocked", "Not Run"],
                                                                required=True
                                                            ),
                                                            "Actual Result": st.column_config.TextColumn("Actual Result", width="medium"),
                                                            "Defect ID": st.column_config.TextColumn("Defect ID", width="small"),
                                                            "Comments": st.column_config.TextColumn("Comments", width="medium")
                                                        },
                                                        key=f"execution_df_{tc_id}"
                                                    )

                        else:
                            st.warning("No test case ID column found in the data.")

                        # Add a button to export the test cases to Excel
                        if st.button("Export to Excel", key="test_analysis_export_button"):
                            try:
                                # Create a temporary Excel file with the test cases
                                test_cases_dir = Path("Test_cases")
                                test_cases_dir.mkdir(exist_ok=True)
                                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                                temp_file = f"TestRun_{selected_run_id}_{jira_id}_{test_type}_{timestamp}.xlsx"
                                temp_file_path = os.path.join(test_cases_dir, temp_file)

                                # Process the dataframe to format it the same way as displayed in the dashboard
                                # Create a copy of the original dataframe for Excel export
                                excel_df = test_cases_df.copy()

                                # Save to Excel
                                create_formatted_excel_from_scenarios(
                                    excel_df,
                                    temp_file_path,
                                    is_dataframe=True,
                                    save_to_db=False,
                                    create_excel=True  # Explicitly create Excel file for download
                                )

                                # Store the path in session state for the download button
                                st.session_state["test_run_excel_path"] = temp_file_path
                                st.session_state["test_run_excel_filename"] = temp_file

                                st.success(f"Excel file prepared successfully: {temp_file}")
                            except Exception as e:
                                st.error(f"Error preparing Excel file: {str(e)}")

                        # Show download button if the Excel file has been prepared
                        if "test_run_excel_path" in st.session_state and os.path.exists(st.session_state["test_run_excel_path"]):
                            with open(st.session_state["test_run_excel_path"], "rb") as file:
                                st.download_button(
                                    label="📥 Download Test Cases",
                                    data=file,
                                    file_name=st.session_state["test_run_excel_filename"],
                                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                    key="test_analysis_download_button"
                                )
                        else:
                            st.warning(f"No test cases found for test run {selected_run_id}.")
            else:
                st.info("No test runs found for your user. Generate some test cases to see them here.")

        # Tab 2: Visualization - Real data visualizations
        with analysis_tabs[1]:
            st.subheader("Test Case Visualization")
            st.info("Visualize your test case data to gain insights into your testing coverage and distribution.")

            # Import necessary libraries
            import pandas as pd
            import plotly.express as px
            import plotly.graph_objects as go

            # Get all test runs for the current user
            all_test_runs = db.get_test_runs_by_user(db.DATABASE_PATH, current_user)

            # Collect all test cases from all test runs
            all_test_cases = []
            if all_test_runs:
                for run in all_test_runs:
                    # Get test cases for each test run
                    test_cases = db.get_test_cases_by_test_run(db.DATABASE_PATH, run['id'])
                    # Check if test_cases is a list and not empty
                    if isinstance(test_cases, list) and len(test_cases) > 0:
                        all_test_cases.extend(test_cases)
                    # Check if test_cases is a DataFrame and not empty
                    elif hasattr(test_cases, 'empty') and not test_cases.empty:
                        # Convert DataFrame to list of dictionaries
                        all_test_cases.extend(test_cases.to_dict('records'))

            if len(all_test_cases) > 0:
                # Convert to DataFrame for easier manipulation
                df_all = pd.DataFrame(all_test_cases)

                # Create tabs for different visualizations
                viz_tabs = st.tabs(["Test Type Distribution", "JIRA Coverage", "Test Status", "Priority Distribution"])

                # Tab 1: Test Type Distribution
                with viz_tabs[0]:
                    st.markdown("### Test Case Distribution by Type")

                    # Count test cases by test type
                    if "Test Type" in df_all.columns:
                        # Clean and standardize test type values
                        df_all["Test Type"] = df_all["Test Type"].fillna("Unknown")
                        df_all["Test Type"] = df_all["Test Type"].str.lower()
                        df_all["Test Type"] = df_all["Test Type"].str.capitalize()

                        # Count test cases by type
                        test_type_counts = df_all["Test Type"].value_counts()

                        # Create a pie chart
                        fig = px.pie(
                            values=test_type_counts.values,
                            names=test_type_counts.index,
                            title="Test Case Distribution by Type",
                            color_discrete_sequence=px.colors.qualitative.Safe,
                            hole=0.4
                        )
                        fig.update_traces(textposition='inside', textinfo='percent+label')
                        fig.update_layout(
                            legend=dict(orientation="h", yanchor="bottom", y=-0.2, xanchor="center", x=0.5),
                            height=500
                        )

                        # Display the chart
                        st.plotly_chart(fig, use_container_width=True)

                        # Display the counts as a table
                        st.markdown("#### Test Case Counts by Type")
                        count_df = pd.DataFrame({
                            "Test Type": test_type_counts.index,
                            "Count": test_type_counts.values
                        })
                        st.dataframe(count_df, use_container_width=True)
                    else:
                        st.warning("No Test Type data available for visualization.")

                # Tab 2: JIRA Coverage
                with viz_tabs[1]:
                    st.markdown("### Test Coverage by JIRA Issue")

                    if "User Story ID" in df_all.columns:
                        # Clean and standardize JIRA IDs
                        df_all["User Story ID"] = df_all["User Story ID"].fillna("Unknown")
                        df_all["User Story ID"] = df_all["User Story ID"].astype(str)

                        # Count test cases by JIRA ID
                        jira_counts = df_all["User Story ID"].value_counts().head(15)  # Top 15 JIRA issues

                        # Create a horizontal bar chart
                        fig = px.bar(
                            x=jira_counts.values,
                            y=jira_counts.index,
                            orientation='h',
                            title="Test Case Coverage by JIRA Issue (Top 15)",
                            labels={"x": "Number of Test Cases", "y": "JIRA Issue ID"},
                            color=jira_counts.values,
                            color_continuous_scale=px.colors.sequential.Viridis
                        )
                        fig.update_layout(height=600)

                        # Display the chart
                        st.plotly_chart(fig, use_container_width=True)

                        # Show JIRA issues with low coverage
                        low_coverage = jira_counts[jira_counts < 5].sort_values()
                        if not low_coverage.empty:
                            st.markdown("#### JIRA Issues with Low Test Coverage (<5 test cases)")
                            low_df = pd.DataFrame({
                                "JIRA Issue": low_coverage.index,
                                "Test Case Count": low_coverage.values
                            })
                            st.dataframe(low_df, use_container_width=True)
                    else:
                        st.warning("No JIRA Issue data available for visualization.")

                # Tab 3: Test Status
                with viz_tabs[2]:
                    st.markdown("### Test Case Status Distribution")

                    if "Test Status" in df_all.columns:
                        # Clean and standardize test status values
                        df_all["Test Status"] = df_all["Test Status"].fillna("Not Run")

                        # Count test cases by status
                        status_counts = df_all["Test Status"].value_counts()

                        # Create a bar chart with custom colors
                        status_colors = {
                            "Pass": "#4CAF50",  # Green
                            "Fail": "#F44336",  # Red
                            "Blocked": "#FF9800",  # Orange
                            "Not Run": "#9E9E9E"  # Gray
                        }

                        # Map colors to statuses
                        colors = [status_colors.get(status, "#2196F3") for status in status_counts.index]

                        # Create the chart
                        fig = go.Figure(data=[
                            go.Bar(
                                x=status_counts.index,
                                y=status_counts.values,
                                marker_color=colors
                            )
                        ])
                        fig.update_layout(
                            title="Test Case Status Distribution",
                            xaxis_title="Status",
                            yaxis_title="Number of Test Cases",
                            height=500
                        )

                        # Display the chart
                        st.plotly_chart(fig, use_container_width=True)

                        # Calculate pass rate
                        total_executed = sum(status_counts[status] for status in status_counts.index if status != "Not Run")
                        passed = status_counts.get("Pass", 0)

                        if total_executed > 0:
                            pass_rate = (passed / total_executed) * 100

                            # Display pass rate in a metric
                            st.metric("Test Pass Rate", f"{pass_rate:.1f}%",
                                      delta=f"{passed} passed out of {total_executed} executed")

                            # Add a gauge chart for pass rate
                            fig = go.Figure(go.Indicator(
                                mode="gauge+number",
                                value=pass_rate,
                                title={"text": "Test Pass Rate"},
                                gauge={
                                    "axis": {"range": [0, 100]},
                                    "bar": {"color": "#4CAF50"},
                                    "steps": [
                                        {"range": [0, 50], "color": "#F44336"},
                                        {"range": [50, 80], "color": "#FF9800"},
                                        {"range": [80, 100], "color": "#4CAF50"}
                                    ],
                                    "threshold": {
                                        "line": {"color": "black", "width": 4},
                                        "thickness": 0.75,
                                        "value": 90
                                    }
                                }
                            ))
                            fig.update_layout(height=300)
                            st.plotly_chart(fig, use_container_width=True)
                    else:
                        st.warning("No Test Status data available for visualization.")

                # Tab 4: Priority Distribution
                with viz_tabs[3]:
                    st.markdown("### Test Case Priority Distribution")

                    if "Priority" in df_all.columns:
                        # Clean and standardize priority values
                        df_all["Priority"] = df_all["Priority"].fillna("Medium")

                        # Count test cases by priority
                        priority_counts = df_all["Priority"].value_counts()

                        # Define priority order and colors
                        priority_order = ["High", "Medium", "Low"]
                        priority_colors = {"High": "#F44336", "Medium": "#FF9800", "Low": "#4CAF50"}

                        # Reindex to ensure correct order
                        priority_counts = priority_counts.reindex(priority_order, fill_value=0)

                        # Create a donut chart
                        fig = px.pie(
                            values=priority_counts.values,
                            names=priority_counts.index,
                            title="Test Case Priority Distribution",
                            color=priority_counts.index,
                            color_discrete_map=priority_colors,
                            hole=0.6
                        )
                        fig.update_traces(textposition='inside', textinfo='percent+label')
                        fig.update_layout(height=500)

                        # Display the chart
                        st.plotly_chart(fig, use_container_width=True)

                        # Display priority counts as metrics
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("High Priority", priority_counts.get("High", 0),
                                      delta=f"{(priority_counts.get('High', 0) / sum(priority_counts) * 100):.1f}%")
                        with col2:
                            st.metric("Medium Priority", priority_counts.get("Medium", 0),
                                      delta=f"{(priority_counts.get('Medium', 0) / sum(priority_counts) * 100):.1f}%")
                        with col3:
                            st.metric("Low Priority", priority_counts.get("Low", 0),
                                      delta=f"{(priority_counts.get('Low', 0) / sum(priority_counts) * 100):.1f}%")
                    else:
                        st.warning("No Priority data available for visualization.")
            else:
                st.warning("No test case data available for visualization. Generate some test cases first.")

        # Tab 3: Reporting - For future reporting features
        with analysis_tabs[2]:
            st.subheader("Test Case Reporting")
            st.info("This section will contain reporting features for test cases in future updates.")

            # Placeholder for future reporting features
            st.markdown("""
            Future reporting features will include:
            - Test execution summary reports
            - Defect tracking and analysis
            - Test coverage reports
            - Custom report generation
            """)

            # Add a sample form for report generation
            st.markdown("### Sample Report Configuration (Placeholder)")

            # Create columns for the form
            col1, col2 = st.columns(2)

            with col1:
                st.selectbox("Report Type", ["Test Execution Summary", "Defect Analysis", "Coverage Report"])
                st.multiselect("Include Test Types", ["Positive", "Negative", "Security", "Performance"])

            with col2:
                st.date_input("Start Date")
                st.date_input("End Date")

            # Add a generate button
            st.button("Generate Report", disabled=True)


# --- Generation and Display ---

def merge_excel_files(input_files, output_file, creating_new_main_file=False):
    """Merges multiple Excel files into a single Excel file with all Test cases in one sheet.

    Args:
        input_files: List of input Excel files to merge
        output_file: Name of the output Excel file
        creating_new_main_file: Whether we're creating a new main file (if True, don't include existing main file data)
    """
    # Create a combined DataFrame
    all_data = []

    # Ensure input_files is a list and contains valid files
    if not input_files:
        st.warning("No input files provided for merging.")
        return None

    # Filter out any files that don't exist
    valid_input_files = [f for f in input_files if os.path.exists(f)]
    if not valid_input_files:
        st.warning("None of the input files exist.")
        return None

    if len(valid_input_files) < len(input_files):
        st.warning(f"Some input files don't exist. Processing {len(valid_input_files)} out of {len(input_files)} files.")

    # Check if the main file exists and read it if it does
    test_cases_dir = Path("Test_cases")
    test_cases_dir.mkdir(exist_ok=True)  # Ensure the directory exists
    main_file_path = test_cases_dir / output_file

    # Check if this is a merge to the main ALL file
    is_main_all_file = "_ALL.xlsx" in output_file and not any(c.isdigit() for c in output_file.split('_')[-1])

    # Extract JIRA ID and test type from the output file name
    parts = output_file.split('_')
    jira_id = None
    test_type = None

    if len(parts) >= 3:
        # Format is typically Complete_testcases_TP-1_ALL.xlsx or Complete_testcases_TP-1_positive.xlsx
        jira_id = parts[2]  # TP-1
        if len(parts) >= 4:
            test_type = parts[3].split('.')[0].lower()  # ALL or positive

    # Get the main file status from session state
    main_file_status = None
    if jira_id and test_type:
        main_file_status = st.session_state.get(f"main_file_status_{jira_id}_{test_type}", {})
        # Override creating_new_main_file if we have it in session state
        if "creating_new" in main_file_status:
            creating_new_main_file = main_file_status.get("creating_new")

    if main_file_path.exists() and not creating_new_main_file:
        try:
            # Read the existing main file
            main_df = pd.read_excel(main_file_path)

            # Only include the main file data if we're appending to the main file
            all_data.append(main_df)
            st.info(f"Found existing main file: {main_file_path}. Will append new test cases.")
        except Exception as e:
            st.warning(f"Error reading existing main file: {str(e)}. Creating a new file.")

    for file in valid_input_files:
        try:
            # Read the Excel file
            df = pd.read_excel(file)

            # Extract test type from filename to clearly identify the test type
            file_basename = os.path.basename(file)
            parts = file_basename.split('_')

            # Initialize test_type
            test_type = ""

            # First, try to get the test type from the Test Type column if it exists
            if "Test Type" in df.columns:
                # Get unique non-empty test types
                unique_test_types = df["Test Type"].dropna().unique()
                if len(unique_test_types) > 0 and unique_test_types[0]:
                    test_type = unique_test_types[0]

            # If we couldn't get the test type from the DataFrame, extract it from the filename
            if not test_type:
                # Handle different filename formats
                if len(parts) >= 4 and parts[-2].lower() in ['positive', 'negative', 'security', 'performance', 'all']:
                    test_type = parts[-2].upper()
                else:
                    # Try to extract from the last part before the extension
                    test_type = parts[-1].split('.')[0].upper()

            # Add a column for test type if it doesn't exist
            if "Test Type" not in df.columns:
                # Only set Test Type for rows that have a Test Case ID
                test_case_id_mask = df["Test Case ID"].notna() & (df["Test Case ID"] != "")
                df.loc[test_case_id_mask, "Test Type"] = test_type

            # Determine the group number based on the test type
            group_number = 1  # Default group number for POSITIVE
            if test_type == "NEGATIVE":
                group_number = 2
            elif test_type == "SECURITY":
                group_number = 3
            elif test_type == "PERFORMANCE":
                group_number = 4

            # Set the Test Group value with the fixed group number based on test type
            # Only set Test Group for rows that have a Test Case ID
            test_case_id_mask = df["Test Case ID"].notna() & (df["Test Case ID"] != "")
            df.loc[test_case_id_mask, "Test Group"] = f"Group {group_number}: {test_type}"

            # Add to our combined data
            all_data.append(df)

        except Exception as e:
            st.error(f"Error processing file {file}: {str(e)}")

    # Combine all DataFrames
    if all_data:
        # Find the highest Test Case ID number if we're appending to the main file
        start_id_from = None
        if is_main_all_file and len(all_data) > 1 and "Test Case ID" in all_data[0].columns:
            # Extract the highest test case ID from the main file
            try:
                # Get all Test Case IDs from the first DataFrame (main file)
                test_case_ids = all_data[0]["Test Case ID"].dropna().unique()

                # Extract numeric parts and find the highest
                highest_id = 0
                for tc_id in test_case_ids:
                    if isinstance(tc_id, str) and tc_id.startswith("TC_"):
                        try:
                            id_num = int(tc_id.split("_")[1])
                            highest_id = max(highest_id, id_num)
                        except (ValueError, IndexError):
                            pass

                if highest_id > 0:
                    start_id_from = highest_id
                    print(f"Continuing Test Case IDs from {highest_id}")
            except Exception as e:
                st.warning(f"Could not determine highest Test Case ID: {e}")

        combined_df = pd.concat(all_data, ignore_index=True)

        # Ensure the main file is saved in the Test_cases directory
        main_output_path = test_cases_dir / output_file

        # Fix Test Group values based on Test Type before saving
        if "Test Type" in combined_df.columns and "Test Group" in combined_df.columns:
            # Only update rows with a Test Case ID
            test_case_id_mask = combined_df["Test Case ID"].notna() & (combined_df["Test Case ID"] != "")

            # For each test type, set the correct group number
            for idx, row in combined_df[test_case_id_mask].iterrows():
                test_type = row["Test Type"]
                if pd.notna(test_type) and test_type:
                    # Determine the group number based on the test type
                    group_number = 1  # Default group number for POSITIVE
                    if test_type == "NEGATIVE":
                        group_number = 2
                    elif test_type == "SECURITY":
                        group_number = 3
                    elif test_type == "PERFORMANCE":
                        group_number = 4

                    # Set the Test Group value
                    combined_df.loc[idx, "Test Group"] = f"Group {group_number}: {test_type}"

        # Save to Excel with proper formatting
        try:
            # Use the helper function to create a formatted Excel file
            create_formatted_excel_from_scenarios(
                combined_df,  # Pass the combined dataframe directly
                str(main_output_path),  # Convert Path to string
                is_dataframe=True,  # Flag to indicate input is a DataFrame
                start_id_from=start_id_from,  # Pass the starting ID if we found one
                continue_numbering=True  # Always continue numbering when merging files
            )
            st.success(f"✅ Successfully merged test cases into {main_output_path}")
            return str(main_output_path)
        except Exception as e:
            st.error(f"Error saving merged file: {str(e)}")
            # Fallback to simple Excel save
            combined_df.to_excel(main_output_path, index=False)
            st.warning(f"Saved unformatted file to {main_output_path} due to formatting error")
            return str(main_output_path)
    else:
        st.warning("No data found to combine")
        return None

if st.session_state["current_page"] == "generator" and generate_button and not disable_generate:
    with st.spinner(f"🔄 Generating test cases-- Please wait."):
        try:
            if test_type == "all":
                all_test_types = ["positive", "negative", "security", "performance"]
                all_responses = []
                all_output_files = []
                all_issues = []
                total_processing_time = 0
                total_tokens_used = 0

                progress_bar = st.progress(0)
                status_text = st.empty()

                # We'll store raw responses in the Test_cases folder
                test_cases_dir = Path("Test_cases")
                test_cases_dir.mkdir(exist_ok=True)

                # Import database helper
                import Test_case_db_helper as db

                # We'll use the database to track test case IDs, no need to check Excel files
                highest_id = 0
                main_file_exists = False
                main_all_file = None

                # Track if we're creating a new main file
                creating_new_main_file = not main_file_exists

                # Store this information in session state
                if f"main_file_status_{case_id}_all" not in st.session_state:
                    st.session_state[f"main_file_status_{case_id}_all"] = {}

                st.session_state[f"main_file_status_{case_id}_all"] = {
                    "exists": main_file_exists,
                    "path": str(main_all_file) if main_all_file else None,
                    "creating_new": creating_new_main_file,
                    "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S")
                }

                if main_file_exists:
                    try:
                        # Read the Excel file
                        main_df = pd.read_excel(main_all_file)

                        # Extract the highest test case ID
                        if "Test Case ID" in main_df.columns:
                            test_case_ids = main_df["Test Case ID"].dropna().unique()
                            for tc_id in test_case_ids:
                                if isinstance(tc_id, str) and tc_id.startswith("TC_"):
                                    try:
                                        id_num = int(tc_id.split("_")[1])
                                        highest_id = max(highest_id, id_num)
                                    except (ValueError, IndexError):
                                        pass
                    except Exception as e:
                        st.warning(f"Could not determine highest Test Case ID from main file: {e}")

                # Import the helper functions for test case counter management
                from helpers import reset_test_case_counter, set_test_case_counter, get_test_case_counter

                # Reset the counter if we're creating a new main file
                if creating_new_main_file:
                    reset_test_case_counter()
                    print(f"Creating new main file for {case_id} (ALL). Starting from TC_001.")
                # If we found a highest ID, set the counter to start from there
                elif highest_id > 0:
                    set_test_case_counter(highest_id)
                    print(f"Continuing Test Case IDs from {highest_id}")

                # Initialize the session state counter for tracking across test types
                st.session_state["all_test_types_counter"] = get_test_case_counter()

                # Create a single test run for the "all" test type
                # Get the current user from session state (if logged in)
                current_user = st.session_state.get("admin_username", "anonymous")

                # Create a test run first
                import Test_case_db_helper as db
                test_run_id = db.create_test_run(
                    db.DATABASE_PATH,
                    case_id,
                    "all",  # Always use "all" as the test type for the test run
                    user_name=current_user,
                    notes=f"Generated with {ai_provider} using {selected_model}"
                )

                # Track the total number of test cases generated
                total_test_cases = 0

                for idx, tt in enumerate(all_test_types):
                    test_type_name = {
                        "positive": "POSITIVE - Success Cases",
                        "negative": "NEGATIVE - Error Cases",
                        "security": "SECURITY - Security Tests",
                        "performance": "PERFORMANCE - Speed & Load"
                        }.get(tt, tt.upper())

                    status_text.text(f"Generating {test_type_name} ({idx+1}/{len(all_test_types)})...")

                    # For all test types, we want to use the same counter to ensure sequential IDs
                    # For the first test type, get the highest ID across all test types
                    if idx == 0:
                        # Get the highest ID across all test types for this JIRA ID
                        import Test_case_db_helper as db
                        highest_id = db.get_highest_test_case_id_number(db.DATABASE_PATH, case_id, "all")
                        if highest_id > 0:
                            # Set the counter to the highest ID
                            set_test_case_counter(highest_id)
                            print(f"Continuing Test Case IDs from TC_{highest_id+1:03d} for {tt.upper()}")
                        else:
                            # If no test cases found, reset the counter
                            reset_test_case_counter()
                            st.info(f"No existing test cases found for {case_id}. Starting from TC_001 for {tt.upper()}")
                    else:
                        # For subsequent test types, continue from the previous test type
                        current_counter = st.session_state.get("all_test_types_counter", 0)
                        # Set the counter to this value
                        set_test_case_counter(current_counter)
                        print(f"Continuing Test Case IDs from TC_{current_counter+1:03d} for {tt.upper()}")

                    # Call generate_test_scenarios with the specific test type
                    # Pass is_all_test_types=True to indicate this is part of the "All Test Case Types" option
                    # Also pass continue_numbering=True to continue test case numbering across test types
                    # Pass the test_run_id to use the same test run for all test types
                    issue, response, output_filename, processing_time, tokens_used = generate_test_scenarios(
                        case_id, tt, num_scenarios, selected_model, jira_client, ai_provider, google_api_key,
                        is_all_test_types=True, continue_numbering=True, test_run_id=test_run_id
                    )

                    # Count the number of test cases generated only if generation was successful
                    if output_filename and response:  # Only count if we have both an output file and a valid response
                        try:
                            # Check if the output_filename is a database URL
                            if output_filename.startswith("database://"):
                                # Extract the JIRA ID and test type from the database URL
                                parts = output_filename.split("/")
                                if len(parts) >= 4:
                                    db_jira_id = parts[2]
                                    db_test_type = parts[3]

                                    # Get the test cases from the database for this specific test run
                                    # This ensures we only count test cases from this specific generation
                                    test_cases_count = db.count_test_cases_for_test_run(db.DATABASE_PATH, test_run_id)
                                    if test_cases_count > 0:
                                        total_test_cases += test_cases_count
                                        print(f"Counted {test_cases_count} test cases from database for test run {test_run_id}")
                                    else:
                                        # Fallback to counting all test cases for this JIRA ID and test type
                                        df = db.get_test_cases_from_database(db.DATABASE_PATH, db_jira_id, db_test_type)

                                        # Count unique test case IDs
                                        if not df.empty and "Test Case ID" in df.columns:
                                            unique_test_case_ids = df["Test Case ID"].dropna().unique()
                                            test_cases_count = len(unique_test_case_ids)
                                            total_test_cases += test_cases_count
                                            print(f"Counted {test_cases_count} test cases from database for {db_jira_id} ({db_test_type})")
                            else:
                                # Read the Excel file to count the test cases
                                df = pd.read_excel(output_filename)
                                if "Test Case ID" in df.columns:
                                    # Count unique test case IDs
                                    unique_test_case_ids = df["Test Case ID"].dropna().unique()
                                    test_cases_count = len(unique_test_case_ids)
                                    total_test_cases += test_cases_count
                                    print(f"Counted {test_cases_count} test cases from Excel file {output_filename}")
                        except Exception as e:
                            print(f"Could not count test cases in {output_filename}: {e}")
                    elif not response:
                        print(f"Skipping test case counting for {tt} as generation failed")

                    # Fix the Test Group values in the generated Excel file
                    if output_filename and not output_filename.startswith("database://") and os.path.exists(output_filename):
                        try:
                            # Read the Excel file
                            df = pd.read_excel(output_filename)

                            # Determine the group number based on the test type
                            group_number = 1  # Default group number for POSITIVE
                            if tt == "negative":
                                group_number = 2
                            elif tt == "security":
                                group_number = 3
                            elif tt == "performance":
                                group_number = 4

                            # Set the Test Group value with the fixed group number based on test type
                            # Only set Test Group for rows that have a Test Case ID
                            test_case_id_mask = df["Test Case ID"].notna() & (df["Test Case ID"] != "")
                            df.loc[test_case_id_mask, "Test Group"] = f"Group {group_number}: {tt.upper()}"

                            # Save the updated Excel file
                            df.to_excel(output_filename, index=False)

                            # Format the Excel file only when explicitly requested
                            create_formatted_excel_from_scenarios(
                                df,
                                output_filename,
                                issue=issue,
                                is_dataframe=True,
                                continue_numbering=True,
                                create_excel=False
                            )
                        except Exception as e:
                            print(f"Could not fix Test Group values in {output_filename}: {e}")
                    elif output_filename and output_filename.startswith("database://"):
                        # For database URLs, we don't need to fix Test Group values in Excel files
                        print(f"Skipping Test Group fix for database URL: {output_filename}")

                    # After generating test cases for this test type, update the counter in session state
                    # Get the current counter value from helpers.py
                    current_counter = get_test_case_counter()
                    st.session_state["all_test_types_counter"] = current_counter
                    print(f"Updated test case counter to {current_counter} after generating {tt.upper()} test cases")

                    # Now create the Excel file with the test type embedded in the name
                    if response:
                        try:
                            # Check if Excel generation succeeded
                            if output_filename and os.path.exists(output_filename):
                                # Add the file to the list of output files
                                all_output_files.append(str(output_filename))
                            else:
                                # Save the raw response to a text file for debugging in the Test_cases folder
                                test_cases_dir = Path("Test_cases")
                                test_cases_dir.mkdir(exist_ok=True)
                                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                raw_file = test_cases_dir / f"Latest_testcases_{case_id}_ALL_{tt}_{timestamp}_raw.txt"
                                try:
                                    with open(raw_file, "w", encoding="utf-8") as f:
                                        f.write(response)
                                    # Save the message to the console but don't display it in the UI
                                    print(f"Saved raw response to {raw_file} for debugging (JSON parsing used fallback mechanism)")
                                except Exception as e:
                                    st.error(f"Failed to save raw response: {e}")
                        except Exception as e:
                            st.error(f"Error processing {tt} scenarios: {str(e)}")

                    # Store results
                    all_issues.append(issue)
                    all_responses.append(response) # Store just the response

                    # Store output file (or None if generation failed)
                    if output_filename and os.path.exists(output_filename):
                        all_output_files.append(output_filename)
                    elif output_filename and output_filename.startswith("database://"):
                        all_output_files.append(output_filename)
                    else:
                        all_output_files.append(None)

                    # Update totals
                    total_processing_time += processing_time
                    if tokens_used is not None:
                        total_tokens_used += tokens_used

                    # Update progress
                    progress_bar.progress((idx + 1) / len(all_test_types))

                status_text.text("Combining results from all test types...")

                # Combine responses into a single string with clear section headers
                combined_response = ""
                for idx, resp in enumerate(all_responses):
                    if resp:  # Only include responses that are not None or empty
                        tt = all_test_types[idx]
                        combined_response += f"\n\n{'=' * 40}\n"
                        combined_response += f"  {tt.upper()} SCENARIOS\n"
                        combined_response += f"{'=' * 40}\n\n"
                        combined_response += resp.strip()

                # Create a combined Excel file with all test cases in one sheet
                # Filter out None values from all_output_files
                valid_output_files = [f for f in all_output_files if f is not None]

                if valid_output_files:
                    # Main file name (without timestamp)
                    combined_output_file = f"Complete_testcases_{case_id}_ALL.xlsx"

                    # Check if main file exists
                    test_cases_dir = Path("Test_cases")
                    main_file_path = test_cases_dir / combined_output_file

                    # Get the main file status from session state
                    main_file_status = st.session_state.get(f"main_file_status_{case_id}_all", {})
                    creating_new_main_file = main_file_status.get("creating_new", not main_file_path.exists())

                    # Create a timestamped version
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    timestamped_file = f"Latest_testcases_{case_id}_ALL_{timestamp}.xlsx"

                    # Check if we have any valid Excel files (not database URLs)
                    excel_files = [f for f in valid_output_files if not f.startswith("database://") and os.path.exists(f)]

                    if excel_files:
                        # Create the timestamped file by merging the individual test type files
                        timestamped_output = merge_excel_files(excel_files, timestamped_file, creating_new_main_file=False)

                        # Fix the Test Group values in the timestamped file
                        if timestamped_output and os.path.exists(timestamped_output):
                            try:
                                # Read the Excel file
                                df = pd.read_excel(timestamped_output)

                                # Fix Test Group values based on Test Type
                                if "Test Type" in df.columns and "Test Group" in df.columns:
                                    # Only update rows with a Test Case ID
                                    test_case_id_mask = df["Test Case ID"].notna() & (df["Test Case ID"] != "")

                                    # For each test type, set the correct group number
                                    for idx, row in df[test_case_id_mask].iterrows():
                                        test_type = row["Test Type"]
                                        if pd.notna(test_type) and test_type:
                                            # Determine the group number based on the test type
                                            group_number = 1  # Default group number for POSITIVE
                                            if test_type == "NEGATIVE":
                                                group_number = 2
                                            elif test_type == "SECURITY":
                                                group_number = 3
                                            elif test_type == "PERFORMANCE":
                                                group_number = 4

                                            # Set the Test Group value
                                            df.loc[idx, "Test Group"] = f"Group {group_number}: {test_type}"

                                # Save the updated Excel file
                                df.to_excel(timestamped_output, index=False)

                                # Format the Excel file only when explicitly requested
                                create_formatted_excel_from_scenarios(
                                    df,
                                    timestamped_output,
                                    issue=all_issues[0],
                                    is_dataframe=True,
                                    continue_numbering=True,
                                    create_excel=False
                                )
                            except Exception as e:
                                st.warning(f"Could not fix Test Group values in {timestamped_output}: {e}")
                    else:
                        # If we only have database URLs, we don't need to create an Excel file
                        timestamped_output = None
                        print("All test cases are stored in the database. No Excel files to merge.")

                        # Now check if main file exists

                    # Only try to merge with main file if we have a timestamped output file
                    if timestamped_output and os.path.exists(timestamped_output):
                        if main_file_path.exists() and not creating_new_main_file:
                            # If main file exists and we're not creating a new one, merge the timestamped file with the main file
                            combined_output_file = merge_excel_files([timestamped_output], combined_output_file, creating_new_main_file=False)
                            st.success(f"Created timestamped file and appended data to main file: {combined_output_file}")
                        else:
                            # If main file doesn't exist or we're creating a new one, use the timestamped file as the main file
                            # Read the timestamped file
                            df = pd.read_excel(timestamped_output)

                            # Fix Test Group values based on Test Type
                            if "Test Type" in df.columns and "Test Group" in df.columns:
                                # Only update rows with a Test Case ID
                                test_case_id_mask = df["Test Case ID"].notna() & (df["Test Case ID"] != "")

                                # For each test type, set the correct group number
                                for idx, row in df[test_case_id_mask].iterrows():
                                    test_type = row["Test Type"]
                                    if pd.notna(test_type) and test_type:
                                        # Determine the group number based on the test type
                                        group_number = 1  # Default group number for POSITIVE
                                        if test_type == "NEGATIVE":
                                            group_number = 2
                                        elif test_type == "SECURITY":
                                            group_number = 3
                                        elif test_type == "PERFORMANCE":
                                            group_number = 4

                                        # Set the Test Group value
                                        df.loc[idx, "Test Group"] = f"Group {group_number}: {test_type}"

                            # Save it as the main file
                            df.to_excel(main_file_path, index=False)
                            # Format the Excel file only when explicitly requested
                            create_formatted_excel_from_scenarios(
                                df,
                                str(main_file_path),
                                issue=all_issues[0],
                                is_dataframe=True,
                                continue_numbering=False,  # Start fresh for the main file
                                create_excel=False
                            )
                            combined_output_file = str(main_file_path)
                            st.success(f"Created new main file: {combined_output_file}")
                    else:
                        # If we don't have a timestamped output file, we're using database storage only
                        combined_output_file = f"database://{case_id}/all/latest"
                        print(f"Test cases saved to database for {case_id} (all)")
                else:
                    combined_output_file = None

                # Save results to session state
                st.session_state.scenario_data = {
                    "issue": all_issues[0],  # Use the first issue (they should all be the same)
                    "response": combined_response,
                    "output_file": combined_output_file,
                    "processing_time": total_processing_time,
                    "ai_provider": ai_provider,
                    "model_used": selected_model,
                    "tokens_used": total_tokens_used,
                    "all_files": all_output_files,  # Store all individual files as well
                    "test_types": all_test_types    # Store the test types
                }

                # Update the test run with the total number of test cases and any errors
                if test_run_id:
                    # Count successful and failed test types
                    successful_types = []
                    failed_types = []

                    for idx, (tt, resp, out_file) in enumerate(zip(all_test_types, all_responses, all_output_files)):
                        if resp and out_file:
                            successful_types.append(tt)
                        else:
                            failed_types.append(tt)

                    # Create a detailed note about the generation
                    if failed_types:
                        notes = f"Generated {total_test_cases} test cases with {ai_provider} using {selected_model}. "
                        notes += f"Successful types: {', '.join(successful_types)}. "
                        notes += f"Failed types: {', '.join(failed_types)}."
                    else:
                        notes = f"Successfully generated {total_test_cases} test cases with {ai_provider} using {selected_model}."

                    db.update_test_run(
                        db.DATABASE_PATH,
                        test_run_id,
                        num_test_cases=total_test_cases,
                        status="completed",
                        notes=notes
                    )

                # Clear progress indicators
                progress_bar.empty()
                status_text.empty()

                # Display success message
                success_message = f"✓ Generated {total_test_cases} test cases across {len(all_test_types)} different test types in {total_processing_time:.1f}s."
                if total_tokens_used and ai_provider == "Google AI Studio":
                    success_message += f" Used approx. {total_tokens_used:,} tokens."
                st.success(success_message)

                # We don't need to store console messages anymore
                pass

                # Add test case statistics
                st.markdown("### Test Case Statistics")

                # Create a more detailed statistics section
                stats_col1, stats_col2, stats_col3 = st.columns(3)
                with stats_col1:
                    # Use the latest test case count from the session state if available
                    latest_count = st.session_state.get("latest_test_case_count", num_scenarios * len(all_test_types))
                    st.metric("Latest Test Cases", latest_count)
                with stats_col2:
                    st.metric("Test Types", len(all_test_types))
                with stats_col3:
                    st.metric("Tokens Used", f"{total_tokens_used:,}")

                # Add a visualization of test cases by type
                if total_test_cases > 0:
                    # Get counts by test type
                    type_counts = []
                    for sub_type in all_test_types:
                        sub_type_df = db.get_test_cases_from_database(db.DATABASE_PATH, case_id, sub_type)
                        if not sub_type_df.empty:
                            count = len(sub_type_df["Test Case ID"].dropna().unique())
                            if count > 0:
                                type_counts.append({"Test Type": sub_type.upper(), "Count": count})

                    if type_counts:
                        # Create a DataFrame for the chart
                        chart_df = pd.DataFrame(type_counts)

                        # Display a bar chart
                        st.markdown("#### Test Cases by Type")
                        chart = alt.Chart(chart_df).mark_bar().encode(
                            x=alt.X('Test Type:N', sort=None),
                            y='Count:Q',
                            color=alt.Color('Test Type:N', scale=alt.Scale(
                                domain=['POSITIVE', 'NEGATIVE', 'SECURITY', 'PERFORMANCE'],
                                range=['#4CAF50', '#FF9800', '#F44336', '#2196F3']
                            )),
                            tooltip=['Test Type:N', 'Count:Q']
                        ).properties(height=200)

                        st.altair_chart(chart, use_container_width=True)

                        # Add a pie chart showing distribution
                        st.markdown("#### Test Case Distribution")
                        pie_chart = alt.Chart(chart_df).mark_arc().encode(
                            theta='Count:Q',
                            color=alt.Color('Test Type:N', scale=alt.Scale(
                                domain=['POSITIVE', 'NEGATIVE', 'SECURITY', 'PERFORMANCE'],
                                range=['#4CAF50', '#FF9800', '#F44336', '#2196F3']
                            )),
                            tooltip=['Test Type:N', 'Count:Q']
                        ).properties(height=200)

                        st.altair_chart(pie_chart, use_container_width=True)

                # Add a simple generation info section
                with st.expander("📋 Generation Summary", expanded=True):
                    st.markdown("### Test Case Generation Summary")

                    # Create a simple summary
                    summary_text = []

                    # Clear any cached data
                    db.close_connection()

                    # Get the test run ID for this generation
                    conn = sqlite3.connect(db.DATABASE_PATH)
                    cursor = conn.cursor()
                    cursor.execute(
                        "SELECT id FROM test_runs WHERE jira_id = ? ORDER BY id DESC LIMIT 1",
                        (case_id,)
                    )
                    latest_test_run = cursor.fetchone()
                    latest_test_run_id = latest_test_run[0] if latest_test_run else None

                    # Get the test cases for the latest test run (this run)
                    latest_run_test_cases = []
                    if latest_test_run_id:
                        cursor.execute(
                            """
                            SELECT test_case_id FROM test_cases
                            WHERE jira_id = ? AND test_run_id = ?
                            ORDER BY CAST(SUBSTR(test_case_id, 4) AS INTEGER)
                            """,
                            (case_id, latest_test_run_id)
                        )
                        latest_run_test_cases = [row[0] for row in cursor.fetchall()]

                    # Get all test cases for this JIRA ID (for total count)
                    cursor.execute(
                        """
                        SELECT COUNT(DISTINCT test_case_id),
                               MIN(CAST(SUBSTR(test_case_id, 4) AS INTEGER)),
                               MAX(CAST(SUBSTR(test_case_id, 4) AS INTEGER))
                        FROM test_cases
                        WHERE jira_id = ? AND test_case_id LIKE 'TC_%'
                        """,
                        (case_id,)
                    )
                    total_result = cursor.fetchone()
                    total_count = total_result[0] if total_result else 0
                    total_min_id = total_result[1] if total_result and total_result[1] else 0
                    total_max_id = total_result[2] if total_result and total_result[2] else 0

                    # Check if attachments were used
                    has_attachments = False
                    if issue and hasattr(issue.fields, 'attachment') and issue.fields.attachment:
                        for att in issue.fields.attachment:
                            if any(att.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']):
                                has_attachments = True
                                break

                    # First show information about the latest test run (this run)
                    if latest_run_test_cases:
                        latest_min = min([int(tc.split('_')[1]) for tc in latest_run_test_cases])
                        latest_max = max([int(tc.split('_')[1]) for tc in latest_run_test_cases])
                        summary_text.append(f"✅ **Latest generation**: {len(latest_run_test_cases)} test cases (TC_{latest_min:03d} - TC_{latest_max:03d})")

                        # Update the stats in session state for the metrics display
                        st.session_state["latest_test_case_count"] = len(latest_run_test_cases)

                    # Then show information about all test cases
                    if total_count > 0:
                        summary_text.append(f"✅ **Total test cases**: {total_count} test cases (TC_{total_min_id:03d} - TC_{total_max_id:03d}) for {case_id}")
                        summary_text.append(f"✅ **All test cases stored in database successfully**")

                    # Add information about attachments
                    if has_attachments:
                        summary_text.append(f"📎 **Attachments used**: JIRA attachments were used as input for test case generation")

                    # Display the summary
                    if summary_text:
                        st.markdown("\n\n".join(summary_text))
                    else:
                        st.info("No test cases have been generated yet.")

                    conn.close()

                # Add an Issues section if there were any issues
                with st.expander("⚠️ Issues", expanded=False):
                    if "generation_issues" in st.session_state and st.session_state["generation_issues"]:
                        st.markdown("### Issues During Generation")
                        for issue in st.session_state["generation_issues"]:
                            st.warning(issue)
                    else:
                        st.success("No issues were encountered during test case generation.")

            else:
                issue, response, output_file, processing_time, tokens_used = generate_test_scenarios(
                    case_id, test_type, num_scenarios, selected_model, jira_client, ai_provider, google_api_key
                )

                # For single test type, follow the same pattern as ALL test type
                # Main file name (without timestamp)
                combined_output_file = f"Complete_testcases_{case_id}_{test_type}.xlsx"

                # Check if main file exists
                test_cases_dir = Path("Test_cases")
                main_file_path = test_cases_dir / combined_output_file

                # We're going to skip the extraction from ALL file behavior
                # This ensures that when generating a specific test type, we always generate new test cases
                # instead of extracting from the ALL file

                # Reset the counter if the main file doesn't exist for this specific test type
                # This ensures that when generating a specific test type for the first time, it starts from TC_001
                if not main_file_path.exists():
                    from helpers import reset_test_case_counter, set_test_case_counter
                    reset_test_case_counter()
                    set_test_case_counter(0)  # Explicitly set to 0 to ensure it starts from TC_001
                    print(f"No main file found for {case_id} ({test_type}). Starting from TC_001.")

                # Get the main file status from session state
                main_file_status = st.session_state.get(f"main_file_status_{case_id}_{test_type}", {})
                creating_new_main_file = main_file_status.get("creating_new", not main_file_path.exists())

                # Check if output_file is a database URL
                if output_file and output_file.startswith("database://"):
                    # If it's a database URL, we don't need to do anything with Excel files
                    combined_output_file = output_file
                    print(f"Test cases saved to database for {case_id} ({test_type})")
                elif main_file_path.exists() and not creating_new_main_file:
                    # If main file exists and we're not creating a new one, merge the timestamped file with the main file
                    combined_output_file = merge_excel_files([output_file], combined_output_file, creating_new_main_file=False)
                    st.success(f"Created timestamped file and appended data to main file: {combined_output_file}")
                else:
                    # If main file doesn't exist or we're creating a new one, use the timestamped file as the main file
                    # First, check if output_file is a regular file path
                    if output_file and os.path.exists(output_file):
                        # Read the Excel file
                        df = pd.read_excel(output_file)
                        # Then save it with the main file name
                        df.to_excel(main_file_path, index=False)
                        # Don't create Excel file here - only save to database
                        # Excel files will only be created when the download button is clicked
                        # Keep the timestamped file (don't remove it)
                        # This ensures we have both the main file and the timestamped file
                        combined_output_file = str(main_file_path)
                        st.success(f"Created new main file: {combined_output_file}")
                    else:
                        # If output_file is not a regular file path, use the database URL
                        combined_output_file = f"database://{case_id}/{test_type}/latest"
                        print(f"Test cases saved to database for {case_id} ({test_type})")

                # Save results to session state
                st.session_state.scenario_data = {
                    "issue": issue,
                    "response": response,
                    "output_file": combined_output_file,  # Use the main file
                    "processing_time": processing_time,
                    "ai_provider": ai_provider,
                    "model_used": selected_model,
                    "tokens_used": tokens_used  # Store token count if available
                }
                # Display success message
                success_message = f"✓ Processed using {ai_provider} ({selected_model}) in {processing_time:.1f}s."
                if tokens_used is not None and ai_provider == "Google AI Studio":
                     success_message += f" Used approx. {tokens_used} tokens."
                st.success(success_message)

                # We don't need to store console messages anymore
                pass

                # Add test case statistics
                st.markdown("### Test Case Statistics")
                stats_col1, stats_col2, stats_col3 = st.columns(3)

                with stats_col1:
                    # Use the latest test case count from the session state if available
                    latest_count = st.session_state.get("latest_test_case_count", num_scenarios)
                    st.metric("Latest Test Cases", latest_count)
                with stats_col2:
                    st.metric("Test Type", test_type.upper())
                with stats_col3:
                    st.metric("Tokens Used", f"{tokens_used:,}" if tokens_used else "N/A")

                # Add a simple generation info section
                with st.expander("📋 Generation Summary", expanded=True):
                    st.markdown("### Test Case Generation Summary")

                    # Create a simple summary
                    summary_text = []

                    # Force a fresh query to the database to get the latest data
                    db.close_connection()

                    # Get the test run ID for this generation
                    conn = sqlite3.connect(db.DATABASE_PATH)
                    cursor = conn.cursor()
                    cursor.execute(
                        "SELECT id FROM test_runs WHERE jira_id = ? AND test_type = ? ORDER BY id DESC LIMIT 1",
                        (case_id, test_type)
                    )
                    latest_test_run = cursor.fetchone()
                    latest_test_run_id = latest_test_run[0] if latest_test_run else None

                    # Get the test cases for the latest test run (this run)
                    latest_run_test_cases = []
                    if latest_test_run_id:
                        cursor.execute(
                            """
                            SELECT test_case_id FROM test_cases
                            WHERE jira_id = ? AND test_run_id = ?
                            ORDER BY CAST(SUBSTR(test_case_id, 4) AS INTEGER)
                            """,
                            (case_id, latest_test_run_id)
                        )
                        latest_run_test_cases = [row[0] for row in cursor.fetchall()]

                    # Get all test cases for this JIRA ID and test type (for total count)
                    cursor.execute(
                        """
                        SELECT COUNT(DISTINCT test_case_id),
                               MIN(CAST(SUBSTR(test_case_id, 4) AS INTEGER)),
                               MAX(CAST(SUBSTR(test_case_id, 4) AS INTEGER))
                        FROM test_cases
                        WHERE jira_id = ? AND test_type = ? AND test_case_id LIKE 'TC_%'
                        """,
                        (case_id, test_type)
                    )
                    total_result = cursor.fetchone()
                    total_count = total_result[0] if total_result else 0
                    total_min_id = total_result[1] if total_result and total_result[1] else 0
                    total_max_id = total_result[2] if total_result and total_result[2] else 0

                    # Check if attachments were used
                    has_attachments = False
                    if issue and hasattr(issue.fields, 'attachment') and issue.fields.attachment:
                        for att in issue.fields.attachment:
                            if any(att.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']):
                                has_attachments = True
                                break

                    # First show information about the latest test run (this run)
                    if latest_run_test_cases:
                        latest_min = min([int(tc.split('_')[1]) for tc in latest_run_test_cases])
                        latest_max = max([int(tc.split('_')[1]) for tc in latest_run_test_cases])
                        summary_text.append(f"✅ **Latest generation**: {len(latest_run_test_cases)} test cases (TC_{latest_min:03d} - TC_{latest_max:03d})")

                        # Update the stats in session state for the metrics display
                        st.session_state["latest_test_case_count"] = len(latest_run_test_cases)

                    # Then show information about all test cases
                    if total_count > 0:
                        summary_text.append(f"✅ **Total test cases**: {total_count} test cases (TC_{total_min_id:03d} - TC_{total_max_id:03d}) for {case_id} ({test_type.upper()})")
                        summary_text.append(f"✅ **All test cases stored in database successfully**")

                    # Add information about attachments
                    if has_attachments:
                        summary_text.append(f"📎 **Attachments used**: JIRA attachments were used as input for test case generation")

                    # Display the summary
                    if summary_text:
                        st.markdown("\n\n".join(summary_text))
                    else:
                        st.info("No test cases have been generated yet.")

                    conn.close()

                # Add an Issues section if there were any issues
                with st.expander("⚠️ Issues", expanded=False):
                    if "generation_issues" in st.session_state and st.session_state["generation_issues"]:
                        st.markdown("### Issues During Generation")
                        for issue in st.session_state["generation_issues"]:
                            st.warning(issue)
                    else:
                        st.success("No issues were encountered during test case generation.")

        except Exception as e:
            st.error(f"⚠️ An error occurred during generation: {e}")
            import traceback
            st.code(traceback.format_exc())
            st.session_state.scenario_data = {
                "issue": None,
                "response": None,
                "output_file": None,
                "processing_time": None,
                "ai_provider": st.session_state.get("ai_provider_radio", "Local"),  # Default to "Local"
                "model_used": None,
                "tokens_used": None
            }

# --- Display Results if Available ---
# Force update the session state if it doesn't exist or doesn't have an output file
if "scenario_data" not in st.session_state or not st.session_state.scenario_data:
    # Get the current JIRA ID and test type from the input fields
    current_jira_id = st.session_state.get("jira_case_id_input", "")
    current_test_type = st.session_state.get("test_type_select", "all")

    # Initialize with default values
    st.session_state.scenario_data = {
        "issue": None,
        "response": None,
        "output_file": f"database://{current_jira_id}/{current_test_type}/latest" if current_jira_id else None,
        "processing_time": None,
        "ai_provider": st.session_state.get("ai_provider_radio", "Local"),
        "model_used": None,
        "tokens_used": None
    }

if st.session_state.scenario_data and st.session_state["current_page"] == "generator": # Only display tabs on Generator page
    data = st.session_state.scenario_data
    issue = data["issue"]
    response = data["response"]
    output_file = data["output_file"]

    # Create tabs for organization
    tab1, tab2, tab3, tab4 = st.tabs(["📝 Input Details", "💡 Raw AI Output", "📊 Generated Test Cases", "🕒 Latest Test Cases"])

    with tab1:
        st.markdown('<h2 class="sub-header">JIRA Details</h2>', unsafe_allow_html=True)
        if issue:
            c1, c2 = st.columns(2)
            with c1:
                st.markdown(f"**Issue Key:** {issue.key}") # Use issue.key
                st.markdown(f"**Summary:** {issue.fields.summary}")
            with c2:
                st.markdown(f"**Status:** {issue.fields.status.name}") # Use status.name
                st.markdown(f"**Created:** {datetime.strptime(issue.fields.created, '%Y-%m-%dT%H:%M:%S.%f%z').strftime('%Y-%m-%d')}") # Format date
        else:
            # Get the current JIRA ID from the input field
            current_jira_id = st.session_state.get("jira_case_id_input", "")
            if current_jira_id:
                st.info(f"No JIRA details available for {current_jira_id}. Generate test cases to see JIRA details.")
            else:
                st.info("No JIRA details available. Enter a JIRA ID and generate test cases to see JIRA details.")

        # Display Description or Acceptance Criteria (Check common fields)
        if issue:
            description = issue.fields.description or "No description found."
            # Often acceptance criteria is in a custom field, e.g., 'customfield_10005'
            # You might need to inspect your JIRA instance to find the correct field ID
            acceptance_criteria = getattr(issue.fields, 'customfield_10005', None) # Example field ID

            st.markdown("### Description")
            st.markdown(description, unsafe_allow_html=True)

            if acceptance_criteria:
                 st.markdown("### Acceptance Criteria")
                 st.markdown(acceptance_criteria, unsafe_allow_html=True)

            # Display attachments if available
            if hasattr(issue.fields, 'attachment') and issue.fields.attachment:
                st.markdown("### Attachments")

                # Create a directory for storing attachments if it doesn't exist
                attached_images_dir = Path("attached_images")
                attached_images_dir.mkdir(exist_ok=True)

                # Create columns for displaying attachments
                attachment_cols = st.columns(3)
                col_index = 0

                for att in issue.fields.attachment:
                    # Only display image files
                    if any(att.filename.lower().endswith(ext) for ext in ['.png', '.jpg', '.jpeg', '.gif', '.bmp']):
                        # Check if the attachment is already downloaded
                        unique_name = f"{issue.key}_{datetime.now().strftime('%Y%m%d')}_{att.filename}"
                        file_path = attached_images_dir / unique_name

                        # If the file doesn't exist, download it
                        if not os.path.exists(file_path):
                            try:
                                # Try to get the attachment data
                                try:
                                    data = att.get_data()
                                    if isinstance(data, bytes):
                                        with open(file_path, "wb") as f:
                                            f.write(data)
                                except Exception as e:
                                    # Try to get the content URL directly if available
                                    if hasattr(att, 'content'):
                                        resp = jira_client._session.get(att.content, stream=True)
                                        if resp.status_code == 200:
                                            with open(file_path, "wb") as f:
                                                f.write(resp.content)
                            except Exception as e:
                                print(f"Error downloading attachment {att.filename}: {e}")

                        # Display the attachment if it exists
                        if os.path.exists(file_path):
                            with attachment_cols[col_index % 3]:
                                st.image(str(file_path), caption=att.filename, use_container_width=True)

                                # Add a download button for the attachment
                                with open(file_path, "rb") as file:
                                    st.download_button(
                                        label=f"Download {att.filename}",
                                        data=file,
                                        file_name=att.filename,
                                        mime=f"image/{os.path.splitext(att.filename)[1][1:]}",
                                        key=f"download_attachment_{att.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
                                    )

                            # Move to the next column
                            col_index += 1


    with tab2:
        st.markdown('<h2 class="sub-header">LLM Response</h2>', unsafe_allow_html=True)
        if response:
            provider = data.get('ai_provider', 'Unknown')
            model = data.get('model_used', 'Unknown')
            st.info(f"Raw output from {provider} ({model})")
            st.text_area("Raw Output", response, height=350)
        else:
            st.info("No AI response available. Generate test cases to see the raw AI output.")

    with tab3:
        try:
            # Import database helper
            import Test_case_db_helper as db

            # Get the current JIRA ID and test type from the input fields
            current_jira_id = st.session_state.get("jira_case_id_input", "")
            current_test_type = st.session_state.get("test_type_select", "all")

            # Initialize parsed_df as an empty DataFrame
            parsed_df = pd.DataFrame()

            # First, check if we have a JIRA ID and test type
            if current_jira_id and current_test_type:
                # Get the current user from session state (if logged in)
                current_user = st.session_state.get("admin_username", "")

                # Always try to get test cases from the database first
                if current_user:
                    st.info(f"Getting test cases from database for {current_jira_id} ({current_test_type}) created by {current_user}")
                    st.markdown("""
                    <div style="background-color: #E3F2FD; padding: 10px; border-radius: 5px; border-left: 5px solid #2196F3;">
                        <p><strong>Note:</strong> You are only seeing test cases that <em>you</em> have created.
                        Other users' test cases are not visible to you.</p>
                    </div>
                    """, unsafe_allow_html=True)
                    parsed_df = db.get_test_cases_from_database(db.DATABASE_PATH, current_jira_id, current_test_type, user_name=current_user)
                else:
                    st.warning("You are not logged in. Please log in to view your test cases.")
                    parsed_df = pd.DataFrame()

                # Print debug information
                st.write(f"Database path: {db.DATABASE_PATH}")
                st.write(f"JIRA ID: {current_jira_id}, Test Type: {current_test_type}")
                st.write(f"Found {len(parsed_df)} rows in the database")

                if parsed_df.empty:
                    st.warning(f"⚠️ No test cases found in database for {current_jira_id} ({current_test_type}).")

                    # If no test cases found in database, check if output_file is a regular Excel file path
                    if output_file and not output_file.startswith("database://") and os.path.exists(output_file):
                        st.info(f"Trying to load test cases from Excel file: {output_file}")
                        parsed_df = pd.read_excel(output_file)
                        if not parsed_df.empty:
                            st.success(f"Loaded {len(parsed_df)} rows from Excel file")
                else:
                    st.success(f"Displaying ALL test cases from database for {current_jira_id} ({current_test_type}).")
            else:
                st.warning("⚠️ Please enter a JIRA ID and select a test type to view test cases.")

            # Ensure User Story ID is treated as string
            if not parsed_df.empty and "User Story ID" in parsed_df.columns:
                parsed_df["User Story ID"] = parsed_df["User Story ID"].fillna("").astype(str)

            if parsed_df.empty:
                st.warning("⚠️ No scenarios were parsed from the AI output. Check the 'Raw AI Output' tab.")
            else:
                # If we have test type data, add filters
                if "Test Type" in parsed_df.columns:
                        # Add a filter to let users see specific test types
                        # Handle NaN values by converting them to empty strings and then filtering them out
                        test_types = parsed_df["Test Type"].fillna("").unique()
                        # Filter out empty strings and sort the remaining values
                        available_test_types = sorted([t for t in test_types if t != ""])
                        selected_test_types = st.multiselect(
                            "Filter by Test Type",
                            options=available_test_types,
                            default=available_test_types,
                            key="test_type_filter"
                        )

                        # Apply the filter if any test types are selected
                        if selected_test_types:
                            # First, identify the Test Case IDs that have the selected test types
                            test_case_ids_to_keep = parsed_df[parsed_df["Test Type"].isin(selected_test_types)]["Test Case ID"].unique()

                            # Then, keep all rows that either have one of these Test Case IDs or have a Step No
                            # This ensures we keep all steps for the selected test types
                            parsed_df = parsed_df[parsed_df["Test Case ID"].isin(test_case_ids_to_keep) | parsed_df["Step No"].notna()]

                            if parsed_df.empty:
                                st.warning("No test scenarios match the selected filters.")

                # Data cleaning and type conversion
                text_cols = [
                    "Project", "Feature", "User Story ID", "Test Case ID", "Test Case Objective",
                    "Prerequisite", "Test Steps", "Expected Result", "Actual Result",
                    "Test Status", "Priority", "Defect ID", "Comments", "Test Type",
                    "Test Group"
                ]
                for col in text_cols:
                    if col in parsed_df.columns:
                        parsed_df[col] = parsed_df[col].fillna("").astype(str)
                if "Step No" in parsed_df.columns:
                    # Convert to string to avoid Arrow serialization issues
                    parsed_df["Step No"] = parsed_df["Step No"].astype(str)

                # Display row count summary for user awareness - CHECK FOR COLUMN BEFORE ACCESSING IT - USE UNIQUE TEST CASES INSTEAD OF ROWS
                if "Test Case ID" in parsed_df.columns:
                    # Filter out empty strings and count only valid test case IDs (TC_XXX format)
                    valid_test_case_ids = parsed_df['Test Case ID'].dropna().astype(str)
                    valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                    # Only count IDs that match the TC_XXX pattern
                    valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                    unique_test_case_ids = valid_test_case_ids.unique()
                    st.info(f"Displaying {len(unique_test_case_ids)} unique test cases")
                else:
                    # For error report DataFrames that don't have standard columns
                    if "Error" in parsed_df.columns:
                        st.warning("Displaying error information instead of test cases. See raw output for details.")
                    else:
                        st.info(f"Displaying {len(parsed_df)} rows")

                # Updated column configuration to match the desired order with Timestamp as first column
                column_config = {
                    "Timestamp": st.column_config.TextColumn(width="medium"),  # Add Timestamp column
                    "Project": st.column_config.TextColumn(width="small"),
                    "Feature": st.column_config.TextColumn(width="medium"),
                    "User Story ID": st.column_config.Column(width="small", required=True),  # Use generic Column type
                    "Test Case ID": st.column_config.TextColumn(width="small"),
                    "Test Case Objective": st.column_config.TextColumn(width="medium"),
                    "Prerequisite": st.column_config.TextColumn(width="medium"),
                    "Step No": st.column_config.TextColumn(width="small"),
                    "Test Steps": st.column_config.TextColumn(width="large"),
                    "Expected Result": st.column_config.TextColumn(width="large"),
                    "Actual Result": st.column_config.TextColumn(width="small", default=""),
                    "Test Status": st.column_config.SelectboxColumn(width="small", options=["Pass", "Fail", "Blocked", "Not Run"], default="Not Run"),
                    "Priority": st.column_config.SelectboxColumn(width="small", options=["High", "Medium", "Low"], default="Medium"),
                    "Defect ID": st.column_config.TextColumn(width="small", default=""),
                    "Comments": st.column_config.TextColumn(width="large", default=""),
                    "Test Type": st.column_config.TextColumn(width="small"),    # Test type
                    "Test Group": st.column_config.TextColumn(width="medium")  # Group identifier
                }

                # Ensure all columns in config exist in df
                for col in column_config.keys():
                    if col not in parsed_df.columns:
                        parsed_df[col] = "" # Add missing column with default value

                # Reorder columns based on config
                ordered_columns = [col for col in column_config.keys() if col in parsed_df.columns]
                ordered_columns += [col for col in parsed_df.columns if col not in ordered_columns]
                parsed_df = parsed_df[ordered_columns]

                # Apply styling to match Excel format
                styled_df = parsed_df.style.apply(lambda x: [
                    'background-color: #E3F2FD' if x.name == 0 or
                                                 (pd.notna(x['Test Case ID']) and x['Test Case ID'] != '') else
                    '' for _ in x
                ], axis=1)

                # Apply additional styling
                styled_df = styled_df.set_properties(**{
                    'text-align': 'center',
                    'font-family': 'Calibri',
                    'font-size': '11pt',
                    'border': '1px solid #BDBDBD'
                })

                # Display the data editor with styling
                edited_df = st.data_editor(
                    parsed_df,  # We still use the original dataframe for editing
                    use_container_width=True,
                    column_config=column_config,
                    hide_index=True,
                    num_rows="dynamic",
                    height=500,
                    key="editable_scenarios_editor"
                )

                # We don't need a second read-only styled version anymore

                col_save, col_dl, col_spacer = st.columns([1,1,2]) # Adjust layout
                with col_save:
                    if st.button("💾 Save Changes to Excel", key="save_changes_button"):
                        try:
                            # Check if output_file is a database URL
                            if output_file and output_file.startswith("database://"):
                                # Extract JIRA ID and test type from the database URL
                                # Format is database://JIRA_ID/TEST_TYPE/latest
                                parts = output_file.replace("database://", "").split("/")
                                if len(parts) >= 2:
                                    jira_id = parts[0]
                                    test_type = parts[1]

                                    # Save the edited dataframe to the database
                                    db_success = db.save_test_cases_to_database(db.DATABASE_PATH, jira_id, edited_df, test_type)
                                    if db_success:
                                        st.success(f"✅ Changes saved to database for {jira_id} ({test_type})")
                                    else:
                                        st.error(f"❌ Error saving changes to database for {jira_id} ({test_type})")
                                else:
                                    st.error("❌ Invalid database URL format")
                            elif output_file and os.path.exists(os.path.dirname(output_file)):
                                # Re-apply formatting when saving edited data to Excel
                                create_formatted_excel_from_scenarios(
                                    edited_df, # Pass the edited dataframe directly
                                    output_file,
                                    issue=issue,
                                    is_dataframe=True, # Add flag to indicate input is DF
                                    create_excel=True  # Explicitly create Excel file when saving changes
                                )
                                st.success(f"✅ Changes saved and formatted in {output_file}")
                            else:
                                st.error("❌ Invalid output file path")
                        except Exception as e:
                            st.error(f"❌ Error saving changes: {str(e)}")
                with col_dl:
                    # Always create a download button, regardless of output_file
                    try:
                        # Get the current JIRA ID and test type from the input fields or edited dataframe
                        current_jira_id = None
                        current_test_type = None

                        # Try to get JIRA ID from the edited dataframe
                        if not edited_df.empty and "User Story ID" in edited_df.columns:
                            # Get the first non-empty User Story ID
                            user_story_ids = edited_df["User Story ID"].dropna().unique()
                            if len(user_story_ids) > 0:
                                current_jira_id = user_story_ids[0]

                        # If we couldn't get the JIRA ID from the dataframe, try to get it from the session state
                        if not current_jira_id:
                            current_jira_id = st.session_state.get("jira_case_id_input", "")

                        # Try to get dashboard test type from the session state first
                        current_test_type = st.session_state.get("test_type_select", "all")

                        # If we're using the "all" test type, make sure the filename reflects that
                        if current_test_type.lower() == "all":
                            # Force it to be "ALL" for the filename
                            current_test_type = "ALL"

                        # If we have a JIRA ID and test type, add a button to prepare Excel file
                        if current_jira_id and current_test_type:
                            # Add a button to prepare the Excel file
                            if st.button("📥 Prepare Excel Download", key="prepare_excel_download_tab3"):
                                with st.spinner("Preparing Excel file for download..."):
                                    try:
                                        # Create a temporary Excel file with the test cases
                                        test_cases_dir = Path("Test_cases")
                                        test_cases_dir.mkdir(exist_ok=True)
                                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                        temp_file = f"Latest_testcases_{current_jira_id}_{current_test_type}_{timestamp}.xlsx"
                                        temp_file_path = os.path.join(test_cases_dir, temp_file)

                                        # Export the edited dataframe to Excel
                                        create_formatted_excel_from_scenarios(
                                            edited_df,  # Use the current state of the dataframe
                                            temp_file_path,
                                            issue=issue,
                                            is_dataframe=True,
                                            save_to_db=False,  # Don't save back to database
                                            create_excel=True  # Explicitly create Excel file for download
                                        )

                                        # Store the path in session state for the download button
                                        st.session_state["tab3_excel_path"] = temp_file_path
                                        st.session_state["tab3_excel_filename"] = temp_file

                                        st.success(f"Excel file prepared successfully: {temp_file}")
                                    except Exception as e:
                                        st.error(f"Error preparing Excel file: {str(e)}")

                            # Show download button if the Excel file has been prepared
                            if "tab3_excel_path" in st.session_state and os.path.exists(st.session_state["tab3_excel_path"]):
                                with open(st.session_state["tab3_excel_path"], "rb") as file:
                                    st.download_button(
                                        label="📥 Download Test Cases",
                                        data=file,
                                        file_name=st.session_state["tab3_excel_filename"],
                                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", # Correct mime type for xlsx
                                        use_container_width=True,
                                        key="download_button"
                                    )
                        else:
                            st.warning("Could not determine JIRA ID or test type for download.")
                    except Exception as e:
                        st.error(f"Error preparing download: {str(e)}")
                        import traceback
                        st.code(traceback.format_exc())

        except Exception as e:
            st.error(f"Error loading or displaying the Excel data: {e}")
            import traceback
            st.code(traceback.format_exc())

    with tab4:
        # Import database helper
        import Test_case_db_helper as db

        # Get the current JIRA ID and test type from the input fields
        current_jira_id = st.session_state.get("jira_case_id_input", "")
        current_test_type = st.session_state.get("test_type_select", "all")

        # First, try to get the latest test run from the database
        if current_jira_id and current_test_type:
            try:
                # Make sure we're using the correct test type format
                display_test_type = current_test_type
                if current_test_type.lower() == "all":
                    # Force it to be "all" (lowercase) for the database query
                    current_test_type = "all"
                    display_test_type = "ALL"

                # Get the current user from session state (if logged in)
                current_user = st.session_state.get("admin_username", "")

                if current_user:
                    st.info(f"Getting latest test cases for {current_jira_id} ({display_test_type}) created by {current_user} from database...")
                    st.markdown("""
                    <div style="background-color: #E3F2FD; padding: 10px; border-radius: 5px; border-left: 5px solid #2196F3;">
                        <p><strong>Note:</strong> You are only seeing test cases that <em>you</em> have created.
                        Other users' test cases are not visible to you.</p>
                    </div>
                    """, unsafe_allow_html=True)

                    # Get the latest generated test cases directly from the database, filtered by user
                    df = db.get_latest_generated_test_cases(db.DATABASE_PATH, current_jira_id, current_test_type, user_name=current_user)
                else:
                    st.warning("You are not logged in. Please log in to view your test cases.")
                    df = pd.DataFrame()

                if not df.empty:
                    st.success(f"Latest test cases for {current_jira_id} ({display_test_type}) from database")
                    # Get the timestamp from the first row
                    if "Timestamp" in df.columns and not df["Timestamp"].empty:
                        timestamp = df["Timestamp"].iloc[0]
                        st.write(f"Generated on: {timestamp}")

                    # Print debug information
                    st.write(f"Found {len(df)} rows in the database")

                    if not df.empty:
                        # Ensure User Story ID and Step No are treated as strings
                        if "User Story ID" in df.columns:
                            df["User Story ID"] = df["User Story ID"].fillna("").astype(str)
                        if "Step No" in df.columns:
                            df["Step No"] = df["Step No"].astype(str)

                        # Display row count summary
                        if "Test Case ID" in df.columns:
                            # Filter out empty strings and count only valid test case IDs (TC_XXX format)
                            valid_test_case_ids = df['Test Case ID'].dropna().astype(str)
                            valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                            # Only count IDs that match the TC_XXX pattern
                            valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                            unique_test_case_ids = valid_test_case_ids.unique()
                            st.info(f"Displaying {len(unique_test_case_ids)} unique test cases from database")
                        else:
                            st.info(f"Displaying {len(df)} rows from database")

                        # Display the dataframe with custom styling to match Excel format
                        # Create a styled dataframe
                        styled_df = df.style.apply(lambda x: [
                            'background-color: #E3F2FD' if x.name == 0 or
                                                         (pd.notna(x['Test Case ID']) and x['Test Case ID'] != '') else
                            '' for _ in x
                        ], axis=1)

                        # Apply additional styling
                        styled_df = styled_df.set_properties(**{
                            'text-align': 'center',
                            'font-family': 'Calibri',
                            'font-size': '11pt',
                            'border': '1px solid #BDBDBD'
                        })

                        # Display the styled dataframe
                        st.dataframe(styled_df, use_container_width=True, height=500)

                        # Add a download button that creates the Excel file on demand
                        try:
                            if st.button("📥 Prepare Excel Download", key="prepare_excel_download"):
                                with st.spinner("Preparing Excel file for download..."):
                                    try:
                                        test_cases_dir = Path("Test_cases")
                                        test_cases_dir.mkdir(exist_ok=True)
                                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                        # If we're using the "all" test type, make sure the filename reflects that
                                        if current_test_type and current_test_type.lower() == "all":
                                            # Force it to be "ALL" for the filename
                                            temp_file = f"Latest_testcases_{current_jira_id}_ALL_{timestamp}.xlsx"
                                        else:
                                            temp_file = f"Latest_testcases_{current_jira_id}_{current_test_type}_{timestamp}.xlsx"
                                        temp_file_path = os.path.join(test_cases_dir, temp_file)

                                        # Save to Excel
                                        create_formatted_excel_from_scenarios(
                                            df,
                                            temp_file_path,
                                            is_dataframe=True,
                                            save_to_db=False,  # Don't save back to database
                                            create_excel=True   # Explicitly create Excel file for download
                                        )

                                        # Store the path in session state for the download button
                                        st.session_state["latest_excel_path"] = temp_file_path
                                        st.session_state["latest_excel_filename"] = temp_file

                                        st.success(f"Excel file prepared successfully: {temp_file}")
                                    except Exception as e:
                                        st.error(f"Error preparing Excel file: {str(e)}")

                            # Show download button if the Excel file has been prepared
                            if "latest_excel_path" in st.session_state and os.path.exists(st.session_state["latest_excel_path"]):
                                with open(st.session_state["latest_excel_path"], "rb") as file:
                                    st.download_button(
                                        label="📥 Download Excel File",
                                        data=file,
                                        file_name=st.session_state["latest_excel_filename"],
                                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                        key="download_excel_button"
                                    )
                        except Exception as e:
                            st.error(f"Error exporting to Excel: {str(e)}")
                    else:
                        st.warning(f"No test cases found in database for {current_jira_id} ({current_test_type})")
                else:
                    # Fall back to Excel files
                    # Get the latest test case file for the current JIRA ID and test type
                    latest_file_path, latest_file_name = get_latest_test_case_file(current_jira_id, current_test_type)

                    if latest_file_path and latest_file_name:
                        st.write(f"Latest test case file for {current_jira_id} ({current_test_type}): **{latest_file_name}**")

                        # Get file modification time
                        mod_time = datetime.fromtimestamp(os.path.getmtime(latest_file_path))
                        st.write(f"Generated on: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")

                        # Read and display the Excel file
                        try:
                            df = pd.read_excel(latest_file_path)

                            # Ensure User Story ID and Step No are treated as strings
                            if "User Story ID" in df.columns:
                                df["User Story ID"] = df["User Story ID"].fillna("").astype(str)
                            if "Step No" in df.columns:
                                df["Step No"] = df["Step No"].astype(str)

                            # Display row count summary
                            if "Test Case ID" in df.columns:
                                # Filter out empty strings and count only valid test case IDs (TC_XXX format)
                                valid_test_case_ids = df['Test Case ID'].dropna().astype(str)
                                valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.strip() != '']
                                # Only count IDs that match the TC_XXX pattern
                                valid_test_case_ids = valid_test_case_ids[valid_test_case_ids.str.match(r'^TC_\d+$')]
                                unique_test_case_ids = valid_test_case_ids.unique()
                                st.info(f"Displaying {len(unique_test_case_ids)} unique test cases from Excel")
                            else:
                                st.info(f"Displaying {len(df)} rows from Excel")

                            # Display the dataframe with custom styling to match Excel format
                            # Create a styled dataframe
                            styled_df = df.style.apply(lambda x: [
                                'background-color: #E3F2FD' if x.name == 0 or
                                                             (pd.notna(x['Test Case ID']) and x['Test Case ID'] != '') else
                                '' for _ in x
                            ], axis=1)

                            # Apply additional styling
                            styled_df = styled_df.set_properties(**{
                                'text-align': 'center',
                                'font-family': 'Calibri',
                                'font-size': '11pt',
                                'border': '1px solid #BDBDBD'
                            })

                            # Display the styled dataframe
                            st.dataframe(styled_df, use_container_width=True, height=500)

                            # Add a download button
                            with open(latest_file_path, "rb") as file:
                                st.download_button(
                                    label="📥 Download Latest Test Cases",
                                    data=file,
                                    file_name=latest_file_name,
                                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                )
                        except Exception as e:
                            st.error(f"Error loading the latest test case file: {str(e)}")
                    else:
                        if current_jira_id and current_test_type:
                            # Check if main file exists
                            test_cases_dir = Path("Test_cases")
                            if current_test_type == "all":
                                main_file = f"Complete_testcases_{current_jira_id}_ALL.xlsx"
                            else:
                                main_file = f"Complete_testcases_{current_jira_id}_{current_test_type}.xlsx"

                            main_file_path = test_cases_dir / main_file

                            if main_file_path.exists():
                                st.info(f"Main file '{main_file}' exists, but no timestamped files found. Generate test cases again to create a timestamped file.")
                            else:
                                st.info(f"No test case files found for {current_jira_id} ({current_test_type}). Generate test cases to see them here.")
                        else:
                            st.info("No test case files found. Generate test cases to see them here.")
            except Exception as db_error:
                st.error(f"Error getting test cases from database: {str(db_error)}")
                st.info("Falling back to Excel files...")

                # Fall back to Excel files
                # Get the latest test case file for the current JIRA ID and test type
                latest_file_path, latest_file_name = get_latest_test_case_file(current_jira_id, current_test_type)

                if latest_file_path and latest_file_name:
                    # Display Excel file (code omitted for brevity, same as above)
                    st.write(f"Latest test case file for {current_jira_id} ({current_test_type}): **{latest_file_name}**")
                    # ... rest of Excel display code
                else:
                    st.info(f"No test case files found for {current_jira_id} ({current_test_type}). Generate test cases to see them here.")
        else:
            st.info("Enter a JIRA ID and select a test type to view the latest test cases.")



elif st.session_state["current_page"] == "generator" and st.session_state.scenario_data and not st.session_state.scenario_data.get("output_file"):
     # Handle case where AI ran but didn't produce a parsable/savable output
     st.warning("AI generation was attempted, but no valid scenario Excel file was produced. Check 'Raw AI Output' if available.")
     # You might still want to show Input and Raw Output tabs here if the data exists
     # (Code omitted for brevity, but similar logic to above could be added)


# Footer

# Footer Text
st.markdown("""
<div style="text-align: center; color: #666; margin-top: 2rem;">
GretahAI Test Case Generator v1.1 | For support contact: <EMAIL>
</div>
""", unsafe_allow_html=True)